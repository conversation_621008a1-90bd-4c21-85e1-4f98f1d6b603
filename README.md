# iata-orll-fe - frontend

## Prerequisites

Download and install the [NodeJS](https://nodejs.org/en/download/).
It will also install the **NPM** - Node Package Manager.
Install NPM dependencies `npm install`.

## Development server

Run `npm run start` for a dev server. Navigate to `http://localhost:4200/`.
The application will automatically reload if you change any of the source files.

## Build

Run `npm run build-test`, `npm run build-staging` or `npm run build-prod`
to build the project for corresponding environment.
Every build command will include needed properties from `src/environments` directory.
You can adjust corresponding properties per environment there.
The build artifacts will be stored in the `dist/aspac-fe` directory.

## Unit tests

Run `npm run test` to execute the unit tests via [Karma](https://karma-runner.github.io).
During the unit tests run, test coverage will be generated in HTML and LCOV formats and stored at `coverage/aspac` folder.

There is an additional command `npm run test:ci` to run the unit tests in Headless Chrome browser.
It's needed for CI and will send the Code Coverage report to Sonar.

## Code style check

[ESLint](https://eslint.org/) and [Prettier](https://prettier.io/) tools are used to check (and potentially fix)
the code style, formatting, naming conventions issues.
Please check their configurations at `.eslintrc.config.js` and `.prettierrc.json` accordingly.
You can run the check with the command `npm run lint`. Results will be shown in the console output.
You should do this check **everytime** before pushing your code or just setup a pre-push hook for the GIT.

## CI/CD

Bitbucket Pipelines are used as the CI/CD tool.
You can check official documentation [here](https://support.atlassian.com/bitbucket-cloud/docs/build-test-and-deploy-with-pipelines/)
All configurations and pipes you can check at the `bitbucket-pipelines.yml`.

## Code analysis

[SonarCloud](https://sonarcloud.io/projects) is used for the code quality and security vulnerabilities analysis.
It's integrated via Bitbucket Pipelines and checks code for every pull request.
Also, you can install the SonarLint plugin for your IDE and check the code before commit.

## OWASP

The Open Web Application Security Project (OWASP) is an online community that produces freely-available
articles, methodologies, documentation, tools, and technologies in the field of web application security.
You can check security vulnerabilities of your dependencies by running NPM script `npm run owasp` on your
local environment. Results will be stored at `reports` folder.
In addition, there is a command `npm run owasp:ci` that is used for CI and it will fail the pipeline
if there are some High/Critical vulnerabilities were found.
Also, there is a pipe `custom:security` to run OWASP check from the Bitbucket Pipelines.
