<ng-container [formGroup]="currencyForm">
	<mat-form-field class="numerical width-100 negative-mr-1" appearance="outline" floatLabel="always">
		@if (formLabel) {
			<mat-label>{{ formLabel | translate }}</mat-label>
		}
		<input matInput [attr.type]="'hidden'" formControlName="numericalValue">
		<input matInput [(ngModel)]="numericalValue"
			(blur)="onNumericalValueBlur()"
			(ngModelChange)="onNumericalValueChange($event)"
			[ngModelOptions]="{standalone: true}">
		@if (currencyForm.get('numericalValue')?.hasError('pattern')) {
			<mat-error>{{ 'sli.mgmt.pieceList.pattern.decimalNumber2NVD' | translate }}</mat-error>
		}
	</mat-form-field>
	<mat-form-field appearance="outline" class="width-80" floatLabel="always">
		<input type="text" matInput
			formControlName="currencyUnit"
			[matAutocomplete]="autoCurrency">
		<mat-autocomplete #autoCurrency="matAutocomplete">
			@for (currency of filteredCurrency; track currency) {
				<mat-option [value]="currency">{{ currency }}</mat-option>
			}
		</mat-autocomplete>
		<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
	</mat-form-field>
</ng-container>
