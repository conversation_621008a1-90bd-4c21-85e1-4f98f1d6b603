import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatListModule } from '@angular/material/list';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '../destroy-observable/destroy-ref.component';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Organization } from '@shared/models/organization.model';

@Component({
	selector: 'orll-select-org-dialog',
	templateUrl: './select-org-dialog.component.html',
	styleUrl: './select-org-dialog.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatDialogModule, MatListModule, TranslateModule],
})
export class SelectOrgDialogComponent extends DestroyRefComponent implements OnInit {
	orgList: Organization[] = [];
	constructor(
		public readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly cdr: ChangeDetectorRef,
		public dialogRef: MatDialogRef<SelectOrgDialogComponent>,
		@Inject(MAT_DIALOG_DATA) public data: any
	) {
		super();
	}

	ngOnInit(): void {
		this.orgMgmtRequestService
			.getOrgList()
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.orgList = res;
					this.cdr.markForCheck();
				},
			});
	}

	getOrgInfo(orgId: string): void {
		this.orgMgmtRequestService
			.getOrgInfo(orgId)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.dialogRef.close(res);
					this.cdr.markForCheck();
				},
			});
	}
}
