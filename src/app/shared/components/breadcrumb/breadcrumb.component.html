<nav class="iata-breadcrumb" aria-label="breadcrumb">
	<ol class="iata-breadcrumb__list">
		<li class="iata-breadcrumb__item">
			<a class="iata-breadcrumb__link" routerLink="/">
				<span class="iata-breadcrumb__text">{{'common.breadcrumb.home' | translate}}</span>
			</a>
		</li>

		@for (breadcrumb of breadcrumbs; track breadcrumb.url) {
			<li class="iata-breadcrumb__item">
				<span class="iata-breadcrumb__separator">></span>
				<a class="iata-breadcrumb__link" [routerLink]="breadcrumb.url">
					@if (breadcrumb.icon) {
					<mat-icon class="iata-breadcrumb__icon">{{breadcrumb.icon}}</mat-icon>
					}
					<span class="iata-breadcrumb__text">{{breadcrumb.label | translate}}</span>
				</a>
			</li>
		}
	</ol>
</nav>
