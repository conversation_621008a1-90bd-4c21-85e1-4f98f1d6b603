import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MainFooterComponent } from './main-footer.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';

describe('MainFooterComponent', () => {
	let component: MainFooterComponent;
	let fixture: ComponentFixture<MainFooterComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [TranslateModule.forRoot()],
			providers: [
				{
					provide: TranslateService,
					useClass: class MockTranslateService {},
				},
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
		}).compileComponents();

		await TestBed.overrideComponent(MainFooterComponent, {
			set: {
				schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
			},
		});
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(MainFooterComponent);
		component = fixture.componentInstance;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
