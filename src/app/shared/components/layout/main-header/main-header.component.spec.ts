import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MainHeaderComponent } from './main-header.component';
import { TranslateModule } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { AuthService } from '@shared/auth/auth.service';
import { of, throwError } from 'rxjs';
import { By } from '@angular/platform-browser';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { OverlayModule } from '@angular/cdk/overlay';

describe('MainHeaderComponent', () => {
	let component: MainHeaderComponent;
	let fixture: ComponentFixture<MainHeaderComponent>;
	let mockRouter: jasmine.SpyObj<Router>;
	let mockAuthService: jasmine.SpyObj<AuthService>;
	let mockActivatedRoute: Partial<ActivatedRoute>;

	beforeEach(async () => {
		mockRouter = jasmine.createSpyObj('Router', ['navigate', 'createUrlTree', 'serializeUrl'], {
			events: of(new NavigationEnd(1, 'test', 'test')),
		});
		mockAuthService = jasmine.createSpyObj('AuthService', ['login', 'logout']);
		mockActivatedRoute = {};

		spyOn(sessionStorage, 'getItem').and.returnValue('testUser');
		spyOn(sessionStorage, 'setItem');
		spyOn(sessionStorage, 'removeItem');

		await TestBed.configureTestingModule({
			imports: [TranslateModule.forRoot(), MatIconModule, MatTooltipModule, OverlayModule],
			providers: [
				{ provide: Router, useValue: mockRouter },
				{ provide: ActivatedRoute, useValue: mockActivatedRoute },
				{ provide: AuthService, useValue: mockAuthService },
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
		}).compileComponents();

		await TestBed.overrideComponent(MainHeaderComponent, {
			set: {
				schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
			},
		});
	});

	beforeEach(() => {
		fixture = TestBed.createComponent(MainHeaderComponent);
		component = fixture.componentInstance;
		component.userList = [
			{ userId: 'shipper1', orgId: 'org1' },
			{ userId: 'forwarder1', orgId: 'org2' },
		];
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with currentUser from sessionStorage', () => {
		expect(sessionStorage.getItem).toHaveBeenCalledWith('currentUser');
		expect(component.currentUser).toBe('testUser');
	});

	it('should toggle dropdown when isOpen is changed', () => {
		expect(component.isOpen).toBeFalse();

		const userIcon = fixture.debugElement.query(By.css('.user-toggle'));
		// eslint-disable-next-line @typescript-eslint/no-empty-function
		userIcon.triggerEventHandler('click', { stopPropagation: () => {} });
		fixture.detectChanges();

		expect(component.isOpen).toBeTrue();

		// eslint-disable-next-line @typescript-eslint/no-empty-function
		userIcon.triggerEventHandler('click', { stopPropagation: () => {} });
		fixture.detectChanges();

		expect(component.isOpen).toBeFalse();
	});

	it('should display current user ID', () => {
		const userIdElement = fixture.debugElement.query(By.css('.userid'));
		expect(userIdElement.nativeElement.textContent).toBe('testUser');
	});

	describe('onSwitch method', () => {
		it('should switch to shipper user and navigate to sli route', () => {
			const user = { userId: 'shipper1', orgId: 'org1' };
			mockAuthService.login.and.returnValue(of({}));

			component.onSwitch(user);

			expect(sessionStorage.removeItem).toHaveBeenCalledWith('currentUser');
			expect(mockAuthService.logout).toHaveBeenCalled();
			expect(mockAuthService.login).toHaveBeenCalledWith({
				userId: 'shipper1',
				orgId: 'org1',
			});
			expect(sessionStorage.setItem).toHaveBeenCalledWith('currentUser', 'shipper1');
			expect(component.currentUser).toBe('shipper1');
			expect(component.isOpen).toBeFalse();
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli'], { relativeTo: jasmine.any(Object) });
		});

		it('should switch to forwarder user and navigate to hawb route', () => {
			const user = { userId: 'forwarder1', orgId: 'org2' };
			mockAuthService.login.and.returnValue(of({}));

			component.onSwitch(user);

			expect(mockRouter.navigate).toHaveBeenCalledWith(['hawb'], { relativeTo: jasmine.any(Object) });
		});

		it('should handle login error', () => {
			const user = { userId: 'testUser', orgId: 'org3' };
			const errorMsg = 'Login failed';
			mockAuthService.login.and.returnValue(throwError(() => new Error(errorMsg)));

			component.onSwitch(user);

			expect(component.currentUser).not.toBe('testUser');
			expect(component.isOpen).toBeFalse();
		});
	});

	it('should render user list in dropdown', () => {
		expect(component.userList.length).toBe(2);
		expect(component.userList[0].userId).toBe('shipper1');
		expect(component.userList[1].userId).toBe('forwarder1');

		component.isOpen = true;
		fixture.detectChanges();

		expect(component.isOpen).toBeTrue();
	});
});
