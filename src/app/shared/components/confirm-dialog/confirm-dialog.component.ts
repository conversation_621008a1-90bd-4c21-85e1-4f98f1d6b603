import { ChangeDetectionStrategy, Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '../destroy-observable/destroy-ref.component';
import { MatButtonModule } from '@angular/material/button';

@Component({
	selector: 'orll-confirm-dialog',
	templateUrl: './confirm-dialog.component.html',
	styleUrl: './confirm-dialog.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [MatDialogModule, MatButtonModule, TranslateModule],
})
export class ConfirmDialogComponent extends DestroyRefComponent {
	constructor(
		public dialogRef: MatDialogRef<ConfirmDialogComponent>,
		@Inject(MAT_DIALOG_DATA) public data: any
	) {
		super();
	}

	onCancel(): void {
		this.dialogRef.close(false);
	}

	onOk(): void {
		this.dialogRef.close(true);
	}
}
