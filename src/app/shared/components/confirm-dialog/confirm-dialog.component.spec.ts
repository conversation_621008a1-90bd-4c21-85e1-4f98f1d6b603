import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ConfirmDialogComponent } from './confirm-dialog.component';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { TranslateModule } from '@ngx-translate/core';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { By } from '@angular/platform-browser';

describe('ConfirmDialogComponent', () => {
	let component: ConfirmDialogComponent;
	let fixture: ComponentFixture<ConfirmDialogComponent>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<ConfirmDialogComponent>>;

	beforeEach(async () => {
		mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);

		await TestBed.configureTestingModule({
			imports: [MatDialogModule, MatButtonModule, TranslateModule.forRoot(), NoopAnimationsModule],
			providers: [
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: {} },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(ConfirmDialogComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('Dialog actions', () => {
		it('should close dialog with 0 when cancel button is clicked', () => {
			component.onCancel();
			expect(mockDialogRef.close).toHaveBeenCalledWith(false);
		});

		it('should close dialog with 1 when OK button is clicked', () => {
			component.onOk();
			expect(mockDialogRef.close).toHaveBeenCalledWith(true);
		});

		it('should call onCancel when cancel button is clicked', () => {
			spyOn(component, 'onCancel');
			const cancelButton = fixture.debugElement.query(By.css('.orll-confirm-dialog__cancel-button'));
			cancelButton.nativeElement.click();
			expect(component.onCancel).toHaveBeenCalled();
		});

		it('should call onOk when OK button is clicked', () => {
			spyOn(component, 'onOk');
			const okButton = fixture.debugElement.query(By.css('.orll-confirm-dialog__ok-button'));
			okButton.nativeElement.click();
			expect(component.onOk).toHaveBeenCalled();
		});
	});

	describe('Template rendering', () => {
		it('should render dialog content with translation', () => {
			const contentElement = fixture.debugElement.query(By.css('mat-dialog-content div'));
			expect(contentElement).toBeTruthy();
			// Note: Actual translation text would be handled by the TranslateService in a real environment
		});

		it('should render cancel and OK buttons', () => {
			const buttons = fixture.debugElement.queryAll(By.css('button'));
			expect(buttons.length).toBe(2);

			const cancelButton = fixture.debugElement.query(By.css('.orll-confirm-dialog__cancel-button'));
			expect(cancelButton).toBeTruthy();

			const okButton = fixture.debugElement.query(By.css('button[color="primary"]'));
			expect(okButton).toBeTruthy();
		});
	});
});
