import {
	ChangeDetectorRef,
	Component,
	ContentChild,
	EventEmitter,
	forwardRef,
	Input,
	numberAttribute,
	OnInit,
	Output,
	TemplateRef,
} from '@angular/core';
import { AsyncPipe, NgTemplateOutlet } from '@angular/common';
import { MatAutocomplete, MatAutocompleteSelectedEvent, MatAutocompleteTrigger, MatOption } from '@angular/material/autocomplete';
import { MatFormField, MatInput, MatLabel, MatPrefix, MatSuffix } from '@angular/material/input';
import {
	ControlValueAccessor,
	FormControl,
	FormsModule,
	NG_VALUE_ACCESSOR,
	ReactiveFormsModule,
	ValidatorFn,
	Validators,
} from '@angular/forms';
import { debounceTime, Observable, tap } from 'rxjs';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AbstractAutocompleteService } from '@shared/models/autocomplete.model';
import { MatIcon } from '@angular/material/icon';
import { MatIconButton } from '@angular/material/button';
import { MatTooltip } from '@angular/material/tooltip';
import { CodeName } from '@shared/models/code-name.model';
import { MatChipRemove, MatChipRow, MatChipSet } from '@angular/material/chips';

type SelectedValue<T> = T | T[] | null;

@Component({
	selector: 'iata-autocomplete',
	templateUrl: './autocomplete.component.html',
	styleUrls: ['./autocomplete.component.scss'],
	imports: [
		AsyncPipe,
		MatAutocomplete,
		MatAutocompleteTrigger,
		MatInput,
		MatLabel,
		MatOption,
		FormsModule,
		MatFormField,
		ReactiveFormsModule,
		MatIcon,
		MatIconButton,
		MatTooltip,
		MatPrefix,
		MatSuffix,
		MatChipRemove,
		MatChipRow,
		MatChipSet,
		NgTemplateOutlet,
	],
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => AutocompleteComponent),
			multi: true,
		},
	],
})
export class AutocompleteComponent<T extends CodeName> extends DestroyRefComponent implements OnInit, ControlValueAccessor {
	@Input({ required: false }) id?: string;
	@Input({ transform: numberAttribute }) minLength = 3;
	@Input() label = '';
	@Input() isReadonly = false;
	@Input() debounceMs = 500;
	@Input({ required: true }) api: AbstractAutocompleteService<T> = {} as AbstractAutocompleteService<T>;
	@Input() validatorFn: ValidatorFn | null = null;
	@Input() multiple = false;
	@Input() single = false;
	@ContentChild('optionTemplate') optionTemplate: TemplateRef<any> | undefined = undefined;

	@Output() selected: EventEmitter<SelectedValue<T>> = new EventEmitter<SelectedValue<T>>();
	selection = new FormControl();
	options$!: Observable<T[]>;
	touched = false;
	disableControl = false;
	selectedItems: T[] = [];

	constructor(private readonly cdr: ChangeDetectorRef) {
		super();
	}

	@Input() autocompleteDisplayValue = (item: string | T, id: string): string => {
		if (typeof item === 'string') {
			return item;
		} else {
			const isAirport = id === 'airport' ? `${item.code} - ${item.name}` : item?.name;
			return item ? isAirport : '';
		}
	};

	autocompleteDisplaySelectedValue = (item: string | T, id: string): string => {
		if (typeof item === 'string') {
			return item;
		} else {
			const isAirport = id === 'airport' ? item.code : item.name;
			return item ? isAirport : '';
		}
	};

	writeValue(obj: any): void {
		if (this.multiple) {
			this.selectedItems = Array.isArray(obj) ? obj : [];
			this.selection.setValue('');
		} else {
			this.selection.setValue(obj);
		}
		this.onChange(obj);
	}

	get inputIconTooltip() {
		if (this.multiple) {
			return 'Clear all values';
		}
		return this.selection.value ? 'Clear value' : 'Select value';
	}

	registerOnChange(fn: (value: SelectedValue<T>) => void): void {
		this.onChange = fn;
		this.setValidators();
	}

	registerOnTouched(fn: () => void): void {
		this.onTouched = fn;
	}

	setDisabledState(isDisabled: boolean): void {
		this.disableControl = isDisabled;
		if (isDisabled) {
			this.selection.disable();
		} else {
			this.selection.enable();
		}
	}

	eraseValue(event: Event): void {
		if (!this.single) {
			event.preventDefault();
			event.stopPropagation();
			if (this.multiple) {
				this.selectedItems = [];
			}
			this.selection.patchValue('');
			this.onChange(this.multiple ? [] : null);
			if (this.selected) {
				this.selected.emit(this.multiple ? [] : null);
			}
		}
	}

	ngOnInit(): void {
		this.options$ = this.id ? this.api.getOptions('', this.id) : this.api.getOptions('');

		if (this.disableControl) {
			this.selection.disable();
		}
		this.selection.valueChanges
			.pipe(
				tap(() => {
					if (!this.multiple) {
						this.onChange(null);
					}
				}),
				debounceTime(this.debounceMs),
				tap(() => this.markAsTouched()),
				tap((searchParams) => {
					const search = typeof searchParams === 'string' ? searchParams : searchParams?.name;
					this.options$ = this.id ? this.api.getOptions(search ?? '', this.id) : this.api.getOptions(search ?? '');
					this.cdr.markForCheck();
				}),
				takeUntilDestroyed(this.destroyRef)
			)
			.subscribe();
	}

	onSelect(event?: MatAutocompleteSelectedEvent) {
		if (this.multiple) {
			if (event?.option.value && !this.selectedItems.some((item) => item.code === event.option.value.code)) {
				if (this.single) {
					this.selectedItems = [event.option.value];
				} else {
					this.selectedItems.push(event.option.value);
				}
				this.selection.setValue('');
			}
			this.onChange(this.selectedItems);
			if (this.selected) {
				this.selected.emit(this.selectedItems);
			}
		} else {
			this.onChange(event?.option.value);
			if (this.selected) {
				this.selected.emit(event?.option.value);
			}
		}
	}

	remove(item: T): void {
		const index = this.selectedItems.findIndex((selectedItem) => selectedItem.code === item.code);
		if (index >= 0) {
			this.selectedItems.splice(index, 1);
			this.onChange(this.selectedItems);
		}
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars,@typescript-eslint/no-empty-function
	onChange = (value: SelectedValue<T>) => {};

	// eslint-disable-next-line @typescript-eslint/no-empty-function
	onTouched = () => {};

	private markAsTouched() {
		if (!this.touched) {
			this.onTouched();
			this.touched = true;
		}
	}

	private setValidators(): void {
		if (this.validatorFn) {
			this.selection.setValidators(this.validatorFn);
			this.selection.updateValueAndValidity();

			if (this.selection.hasError('required')) {
				this.selection.addValidators(Validators.required);
			}
		}
	}
}
