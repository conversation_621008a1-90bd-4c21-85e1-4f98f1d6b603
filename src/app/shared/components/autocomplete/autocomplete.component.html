<mat-form-field appearance="outline" class="width-full iata-autocomplete" subscriptSizing="dynamic" floatLabel="always">
	<mat-icon matPrefix>search</mat-icon>
	<mat-label>{{ label }}</mat-label>
	<input
		[formControl]="selection"
		[matAutocomplete]="projectAuto"
		[readonly]="isReadonly"
		matInput
		type="text"
	/>
	@if(!isReadonly) {
		<button
			matSuffix
			mat-icon-button
			color="primary"
			type="button"
			[disabled]="disableControl"
			[matTooltip]="inputIconTooltip"
			matTooltipPosition="above"
			(click)="eraseValue($event)">
			<mat-icon class="material-symbols-outlined">{{ single ? 'keyboard_arrow_down' : 'close'}}</mat-icon>
		</button>
	}
	<mat-autocomplete
		#projectAuto="matAutocomplete"
		(optionSelected)="onSelect($event)"
		[displayWith]="autocompleteDisplayValue">
		@for (option of options$ | async; track option) {
			<mat-option [value]="option" class="iata-autocomplete__options">
				<ng-container *ngTemplateOutlet="optionTemplate || defaultOptionTemplate; context: { $implicit: option }"></ng-container>
				<ng-template #defaultOptionTemplate let-option>
					{{ autocompleteDisplayValue(option, id) }}
				</ng-template>
			</mat-option>
		}
	</mat-autocomplete>
</mat-form-field>
@if (multiple) {
	<mat-chip-set class="iata-autocomplete-chips" aria-label="Item selection">
		@for (itemCode of selectedItems; track $index) {
			<mat-chip-row (removed)="remove(itemCode)" class="truncate-chip" [disabled]="disableControl">
				<span class="chip-content">{{ autocompleteDisplaySelectedValue(itemCode, id) }}</span>
				@if (!disabled) {
					<button matChipRemove [attr.aria-label]="'remove ' + itemCode">
						<mat-icon>cancel</mat-icon>
					</button>
				}
			</mat-chip-row>
		}
	</mat-chip-set>
}
