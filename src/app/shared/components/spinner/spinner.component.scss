$cmp-root-selector: '.iata-spinner';

#{$cmp-root-selector} {
	position: absolute;
	top: 50%;
	left: 50%;
	z-index: 999;
	transform: translate(-50%, -50%) rotate(90deg);

	&,
	&__dot-a,
	&__dot-b {
		transform-origin: 50% 50%;
		will-change: transform;
	}

	&__dot-a,
	&__dot-b {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
	}

	&:after,
	&:before,
	&__dot-a:after,
	&__dot-a:before,
	&__dot-b:after,
	&__dot-b:before {
		content: '';
		position: absolute;
		background: #1E32FA;
		border-radius: 50%;
		animation-duration: 1s;
		animation-iteration-count: infinite;
		transform: translate3d(0, 0, 0);
	}

	&__dot-a {
		transform: rotate(60deg);
	}

	&__dot-b {
		transform: rotate(120deg);
	}

	&:before {
		animation-delay: -.083s;
	}

	&__dot-a:before {
		animation-delay: .083s;
	}

	&__dot-b:before {
		animation-delay: 250ms;
	}

	&:after {
		animation-delay: .416s;
	}

	&__dot-a:after {
		animation-delay: .583s;
	}

	&__dot-b:after {
		animation-delay: 750ms;
	}

	&--large {
		height: 2.75rem;
		width: 2.75rem;

		#{$cmp-root-selector}__dot-a:after,
		#{$cmp-root-selector}__dot-a:before,
		#{$cmp-root-selector}__dot-b:after,
		#{$cmp-root-selector}__dot-b:before,
		&#{$cmp-root-selector}:after,
		&#{$cmp-root-selector}:before {
			width: .625rem;
			height: .625rem;
		}

		#{$cmp-root-selector}__dot-a:before,
		#{$cmp-root-selector}__dot-b:before,
		&#{$cmp-root-selector}:before {
			animation-name: dotsBounceBefore-large;
			top: -.3125rem;
			left: -.3125rem;
		}

		#{$cmp-root-selector}__dot-a:after,
		#{$cmp-root-selector}__dot-b:after,
		&#{$cmp-root-selector}:after {
			animation-name: dotsBounceAfter-large;
			top: -.3125rem;
			right: -.3125rem;
		}
	}
}

@keyframes dotsBounceBefore-large {
	0% {
		transform: translate3d(0, 0, 0);
	}
	60% {
		transform: translate3d(0, 0, 0);
		animation-timing-function: cubic-bezier(.55, .085, .68, .53);
	}
	80% {
		transform: translate3d(-.75rem, 0, 0);
		animation-timing-function: cubic-bezier(0, 1.11, .7, 1.43);
	}
	100% {
		transform: translateX(0);
	}
}

@keyframes dotsBounceAfter-large {
	0% {
		transform: translate3d(0, 0, 0);
	}
	60% {
		transform: translate3d(0, 0, 0);
		animation-timing-function: cubic-bezier(.55, .085, .68, .53);
	}
	80% {
		transform: translate3d(.75rem, 0, 0);
		animation-timing-function: cubic-bezier(0, 1.11, .7, 1.43);
	}
	100% {
		transform: translateX(0);
	}
}
