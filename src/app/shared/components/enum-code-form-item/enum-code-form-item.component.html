<input type="text" matInput
	#inputElement
	[ngModel]="enumCode"
	(ngModelChange)="onInputChange($event)"
	[ngModelOptions]="{standalone: true}"
	[disabled]="disabled"
	[matAutocomplete]="autoPackagingType"
	(focusout)="onTouched()">
<mat-autocomplete #autoPackagingType="matAutocomplete"
	[displayWith]="displayEnumCodeName">
	@for (packagingTypes of filteredCodes; track $index) {
		<mat-option [value]="packagingTypes.code">
			{{ packagingTypes.name }}
		</mat-option>
	}
</mat-autocomplete>
