import { Component, DestroyRef, ElementRef, forwardRef, Input, OnInit, ViewChild } from '@angular/core';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { CodeName } from '@shared/models/code-name.model';
import { _IdGenerator, FocusMonitor } from '@angular/cdk/a11y';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';
import { MatFormFieldControl } from '@angular/material/form-field';
import { Subject } from 'rxjs';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';

@Component({
	selector: 'orll-enum-code-form-item',
	imports: [FormsModule, MatAutocompleteModule, MatIconModule, TranslateModule, MatInputModule],
	templateUrl: './enum-code-form-item.component.html',
	styleUrl: './enum-code-form-item.component.scss',
	providers: [
		{
			provide: NG_VALUE_ACCESSOR,
			useExisting: forwardRef(() => EnumCodeFormItemComponent),
			multi: true,
		},
		{ provide: MatFormFieldControl, useExisting: EnumCodeFormItemComponent },
	],
})
export class EnumCodeFormItemComponent implements MatFormFieldControl<any>, ControlValueAccessor, OnInit {
	focused = false;

	readonly stateChanges: Subject<void> = new Subject<void>();

	enumCode = '';

	public filteredCodes: CodeName[] = [];
	public disabled = false;

	// eslint-disable-next-line @typescript-eslint/no-empty-function
	public onTouched: () => void = () => {};
	// eslint-disable-next-line @typescript-eslint/no-empty-function
	public onChanges: (value: string) => void = () => {};

	@ViewChild('inputElement')
	private inputElementRef!: ElementRef<HTMLInputElement>;

	@Input()
	// @ts-expect-error - no error
	override get id(): string | undefined {
		return this._id;
	}

	// @ts-expect-error - no error
	override set id(value: string | undefined) {
		this._id = value || this.uid;
	}

	// eslint-disable-next-line @typescript-eslint/naming-convention
	protected _id: string | undefined;

	protected uid: string | undefined;

	@Input({ required: true })
	enumType!: EnumCodeTypeModel;

	constructor(
		private readonly elementRef: ElementRef<HTMLElement>,
		private readonly focusMonitor: FocusMonitor,
		private readonly mgmtRequestService: OrgMgmtRequestService,
		private readonly destroyRef: DestroyRef,
		protected readonly idGenerator: _IdGenerator
	) {
		this.uid = this.idGenerator.getId('mat-input-');
	}

	ngOnInit() {
		this.focusMonitor
			.monitor(this.elementRef, true)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe((focusOrigin) => {
				if (!focusOrigin) {
					Promise.resolve().then(() => this.onTouched());
				}
			});

		this.mgmtRequestService.getEnumCode(this.enumType).subscribe((codes: CodeName[]) => {
			this.filteredCodes = codes;
		});
	}

	writeValue(value: string): void {
		this.enumCode = value;
	}

	registerOnChange(fn: any): void {
		this.onChanges = fn;
	}

	registerOnTouched(fn: any): void {
		this.onTouched = fn;
	}

	setDisabledState?(isDisabled: boolean): void {
		this.disabled = isDisabled;
	}

	setDescribedByIds(ids: string[]): void {
		const element = this.elementRef.nativeElement;

		if (ids.length) {
			element.setAttribute('aria-describedby', ids.join(' '));
		} else {
			element.removeAttribute('aria-describedby');
		}
	}

	onContainerClick(): void {
		if (!this.focused) {
			this.inputElementRef.nativeElement.focus();
		}
	}

	onInputChange(value: any) {
		this.onChanges(value);
		this.filter(value);
		this.enumCode = value;
	}

	displayEnumCodeName = (value: string): string => {
		return value;
	};

	private filter(value: string): CodeName[] {
		const filterValue = value.toLowerCase();

		return this.filteredCodes.filter((option) => option.code.toLowerCase().includes(filterValue));
	}
}
