import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AuthService } from './auth.service';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
	constructor(private readonly authService: AuthService) {}

	intercept(request: HttpRequest<any>, next: <PERSON>ttpHandler): Observable<HttpEvent<any>> {
		const token = this.authService.getToken();

		if (token) {
			request = request.clone({
				setHeaders: {
					Authorization: token,
				},
			});
		}
		return next.handle(request);
	}
}
