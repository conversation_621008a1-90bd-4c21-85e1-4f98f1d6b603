import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ApiService } from '@shared/services/api.service';
import {Base64} from 'js-base64';

@Injectable({
	providedIn: 'root',
})
export class AuthService extends ApiService {
	private readonly tokenKey = 'auth_token';
	private payload: any;

	constructor(http: HttpClient) {
		super(http);
	}

	login(params?: any): Observable<any> {
		return super.getData<string>('user/user/token', params).pipe(
			tap((response) => {
				this.setToken(response);
			}),
			catchError((error) => {
				return throwError(() => error);
			})
		);
	}

	getToken(): string | null {
		const token = localStorage.getItem(this.tokenKey);
		if (token && !this.payload) {
			this.payload = this.decodeJwtPayload(token);
		}
		return token;
	}

	setToken(token: string): void {
		this.payload = this.decodeJwtPayload(token);
		localStorage.setItem(this.tokenKey, token);
	}

	clearToken(): void {
		localStorage.removeItem(this.tokenKey);
	}

	isLoggedIn(): boolean {
		return !!localStorage.getItem(this.tokenKey);
	}

	logout(): void {
		this.clearToken();
	}

	getPayload(): any {
		return this.payload;
	}

	decodeJwtPayload(payload: string): any {
		if (payload.length === 0) {
			throw new Error('Cannot extract from an empty payload.');
		}

		const parts = payload.split('.');

		if (parts.length !== 3) {
			throw new Error(`The payload ${payload} is not valid JWT payload and must consist of three parts.`);
		}

		let decoded: string;
		try {
			decoded = Base64.decode(parts[1]);
		} catch {
			throw new Error(`The payload ${payload} is not valid JWT payload and cannot be parsed.`);
		}

		if (!decoded) {
			throw new Error(`The payload ${payload} is not valid JWT payload and cannot be decoded.`);
		}
		return JSON.parse(decoded);
	}
}
