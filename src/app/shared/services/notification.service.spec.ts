import { TestBed } from '@angular/core/testing';
import { NotificationService } from './notification.service';
import { MatSnackBar } from '@angular/material/snack-bar';

describe('NotificationService', () => {
	let service: NotificationService;
	let snackBarMock: jasmine.SpyObj<MatSnackBar>;

	beforeEach(() => {
		// Create a mock for the MatSnackBar dependency
		snackBarMock = jasmine.createSpyObj('MatSnackBar', ['open']);

		TestBed.configureTestingModule({
			providers: [NotificationService, { provide: MatSnackBar, useValue: snackBarMock }],
		});

		service = TestBed.inject(NotificationService);
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	describe('showSuccess', () => {
		it('should open a snackbar with success styling', () => {
			// Arrange
			const testMessage = 'Success message';

			// Act
			service.showSuccess(testMessage);

			// Assert
			expect(snackBarMock.open).toHaveBeenCalledWith(testMessage, undefined, {
				panelClass: ['snackbar--success'],
				duration: 3000,
				verticalPosition: 'top',
			});
		});
	});

	describe('showError', () => {
		it('should open a snackbar with error styling', () => {
			// Arrange
			const testMessage = 'Error message';

			// Act
			service.showError(testMessage);

			// Assert
			expect(snackBarMock.open).toHaveBeenCalledWith(testMessage, undefined, {
				panelClass: ['snackbar--error'],
				duration: 3000,
				verticalPosition: 'top',
			});
		});
	});

	describe('verticalPosition configuration', () => {
		it('should set verticalPosition to "top" for success notifications', () => {
			// Arrange
			const testMessage = 'Test message';

			// Act
			service.showSuccess(testMessage);

			// Assert
			const callArgs = snackBarMock.open.calls.mostRecent().args[2];
			expect(callArgs?.verticalPosition).toBe('top');
		});

		it('should set verticalPosition to "top" for error notifications', () => {
			// Arrange
			const testMessage = 'Test message';

			// Act
			service.showError(testMessage);

			// Assert
			const callArgs = snackBarMock.open.calls.mostRecent().args[2];
			expect(callArgs?.verticalPosition).toBe('top');
		});
	});

	describe('duration configuration', () => {
		it('should set duration to 3000ms for success notifications', () => {
			// Act
			service.showSuccess('Test message');

			// Assert
			const callArgs = snackBarMock.open.calls.mostRecent().args[2];
			expect(callArgs?.duration).toBe(3000);
		});

		it('should set duration to 3000ms for error notifications', () => {
			// Act
			service.showError('Test message');

			// Assert
			const callArgs = snackBarMock.open.calls.mostRecent().args[2];
			expect(callArgs?.duration).toBe(3000);
		});
	});

	describe('panelClass configuration', () => {
		it('should apply snackbar--success class for success notifications', () => {
			// Act
			service.showSuccess('Test message');

			// Assert
			const callArgs = snackBarMock.open.calls.mostRecent().args[2];
			expect(callArgs?.panelClass).toContain('snackbar--success');
		});

		it('should apply snackbar--error class for error notifications', () => {
			// Act
			service.showError('Test message');

			// Assert
			const callArgs = snackBarMock.open.calls.mostRecent().args[2];
			expect(callArgs?.panelClass).toContain('snackbar--error');
		});
	});
});
