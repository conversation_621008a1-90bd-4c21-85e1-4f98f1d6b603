import { Injectable } from '@angular/core';
import { AuthService } from '@shared/auth/auth.service';
import { environment } from '@environments/environment';

@Injectable({
	providedIn: 'root',
})
export class AppInitializerService {
	constructor(private readonly authService: AuthService) {}

	async initializeApp(): Promise<boolean> {
		return new Promise((resolve) => {
			if (!this.authService.isLoggedIn()) {
				this.authService
					.login({
						userId: `${environment.userId}`,
						orgId: `${environment.orgId}`,
					})
					.subscribe({
						next: () => resolve(true),
						error: () => resolve(false),
					});
			} else {
				resolve(true);
			}
		});
	}
}
