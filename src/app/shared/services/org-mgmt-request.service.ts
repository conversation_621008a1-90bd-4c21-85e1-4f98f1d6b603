import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { Organization } from '../models/organization.model';
import { OrgInfo } from '../models/org-info.model';
import { CodeName } from '@shared/models/code-name.model';
import { map } from 'rxjs/operators';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';

@Injectable({ providedIn: 'root' })
export class OrgMgmtRequestService extends ApiService {
	constructor(http: HttpClient) {
		super(http);
	}

	getOrgList(orgType?: string | null): Observable<Organization[]> {
		return super.getData<Organization[]>('org/org/list', { orgType: orgType ?? '' });
	}

	getOrgInfo(orgId: string): Observable<OrgInfo> {
		return super.getData<OrgInfo>('org/org', { orgId });
	}

	getEnumCode(enumCode: EnumCodeTypeModel): Observable<CodeName[]> {
		return super.getData<CodeName[]>(`sys-management/enums/${enumCode}`).pipe(
			map((res) => {
				return res.map((packageType: CodeName) => {
					return { code: packageType.code, name: packageType.name };
				});
			})
		);
	}
}
