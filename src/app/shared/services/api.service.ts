import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({
	providedIn: 'root',
})
export class ApiService {
	constructor(private readonly http: HttpClient) {}

	// GET
	getData<T>(endpoint: string, params?: any): Observable<T> {
		let httpParams = new HttpParams();
		if (params) {
			Object.keys(params).forEach((key) => {
				httpParams = httpParams.set(key, params[key]);
			});
		}

		return this.http.get<T>(`/prod-api/${endpoint}`, { params: httpParams });
	}

	// POST
	postData<T>(endpoint: string, data: any): Observable<T> {
		return this.http.post<T>(`/prod-api/${endpoint}`, data);
	}

	// PUT
	updateData<T>(endpoint: string, data: any): Observable<T> {
		return this.http.put<T>(`/prod-api/${endpoint}`, data);
	}

	// PATCH
	updateDataPatch<T>(endpoint: string, data: any): Observable<T> {
		return this.http.patch<T>(`/prod-api/${endpoint}`, data);
	}

	// DELETE
	deleteData<T>(endpoint: string, data: any): Observable<T> {
		return this.http.delete<T>(`/prod-api/${endpoint}`, { body: data });
	}
}
