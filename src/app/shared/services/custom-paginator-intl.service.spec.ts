import { TestBed } from '@angular/core/testing';
import { CustomPaginatorIntl } from './custom-paginator-intl.service';
import { TranslateService } from '@ngx-translate/core';
import { Subject } from 'rxjs';

describe('CustomPaginatorIntl', () => {
	let service: CustomPaginatorIntl;
	let translateServiceMock: jasmine.SpyObj<TranslateService>;
	const onLangChangeSubject = new Subject<any>();

	beforeEach(() => {
		translateServiceMock = jasmine.createSpyObj('TranslateService', ['instant'], {
			onLangChange: onLangChangeSubject.asObservable(),
		});

		// Configure the mock to return specific values for different translation keys
		translateServiceMock.instant.and.callFake((key: string) => {
			const translations: Record<string, string> = {
				'pagination.itemsPerPage': 'Records per page:',
				'pagination.nextPage': 'Next page',
				'pagination.previousPage': 'Previous page',
				'pagination.firstPage': 'First page',
				'pagination.lastPage': 'Last page',
			};
			return translations[key] || key;
		});

		TestBed.configureTestingModule({
			providers: [CustomPaginatorIntl, { provide: TranslateService, useValue: translateServiceMock }],
		});

		service = TestBed.inject(CustomPaginatorIntl);
	});

	it('should be created', () => {
		expect(service).toBeTruthy();
	});

	it('should initialize with translated labels', () => {
		expect(service.itemsPerPageLabel).toBe('Records per page:');
		expect(service.nextPageLabel).toBe('Next page');
		expect(service.previousPageLabel).toBe('Previous page');
		expect(service.firstPageLabel).toBe('First page');
		expect(service.lastPageLabel).toBe('Last page');
	});

	it('should update labels when language changes', () => {
		// Change the mock to return different translations
		translateServiceMock.instant.and.callFake((key: string) => {
			const translations: Record<string, string> = {
				'pagination.itemsPerPage': 'Elementos por página:',
				'pagination.nextPage': 'Página siguiente',
				'pagination.previousPage': 'Página anterior',
				'pagination.firstPage': 'Primera página',
				'pagination.lastPage': 'Última página',
			};
			return translations[key] || key;
		});

		// Spy on the changes.next() method
		spyOn(service.changes, 'next');

		// Trigger language change
		onLangChangeSubject.next({});

		// Verify labels were updated
		expect(service.itemsPerPageLabel).toBe('Elementos por página:');
		expect(service.nextPageLabel).toBe('Página siguiente');
		expect(service.previousPageLabel).toBe('Página anterior');
		expect(service.firstPageLabel).toBe('Primera página');
		expect(service.lastPageLabel).toBe('Última página');

		// Verify changes.next() was called
		expect(service.changes.next).toHaveBeenCalled();
	});

	it('should call translateLabels method when language changes', () => {
		spyOn(service, 'translateLabels');

		onLangChangeSubject.next({});

		expect(service.translateLabels).toHaveBeenCalled();
	});

	it('should call translate.instant for each label in translateLabels', () => {
		// Reset the spy count
		translateServiceMock.instant.calls.reset();

		service.translateLabels();

		expect(translateServiceMock.instant).toHaveBeenCalledWith('pagination.itemsPerPage');
		expect(translateServiceMock.instant).toHaveBeenCalledWith('pagination.nextPage');
		expect(translateServiceMock.instant).toHaveBeenCalledWith('pagination.previousPage');
		expect(translateServiceMock.instant).toHaveBeenCalledWith('pagination.firstPage');
		expect(translateServiceMock.instant).toHaveBeenCalledWith('pagination.lastPage');
		expect(translateServiceMock.instant).toHaveBeenCalledTimes(5);
	});
});
