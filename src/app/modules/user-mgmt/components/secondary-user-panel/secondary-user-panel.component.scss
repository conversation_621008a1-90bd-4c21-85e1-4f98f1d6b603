.orll-secondary-user-panel {
	margin: 20px;

	&__add-button {
		color: var(--iata-blue-primary) !important;
		border: 1px solid var(--iata-blue-primary);
	}

	.secondary-user-content {
		display: flex;
		width: 100%;
		justify-content: space-between;
		align-items: center;
		padding: 10px 0;

		.item-info {
			display: flex;
			flex-grow: 1;
			flex-wrap: wrap;
			width: 100%;

			.item-orgName,
			.item-userType {
				word-break: break-word;
				white-space: normal;
				padding: 0 10px;
				line-height: 1.5;
			}

			.value {
				color: var(--iata-blue-primary);
			}
		}
	}

	mat-list-item {
		height: auto !important;
		min-height: 48px;
		::ng-deep .mat-list-item-content {
			height: auto !important;
			padding: 8px 0 !important;
		}
	}
}
