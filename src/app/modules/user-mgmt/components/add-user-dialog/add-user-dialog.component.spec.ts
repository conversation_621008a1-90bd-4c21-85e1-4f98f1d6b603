import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AddUserDialogComponent } from './add-user-dialog.component';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { TranslateModule } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { UserMgmtRequestService } from '../../services/user-mgmt-request.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { of } from 'rxjs';

describe('AddUserDialogComponent', () => {
	let component: AddUserDialogComponent;
	let fixture: ComponentFixture<AddUserDialogComponent>;
	let mockDialogRef: jasmine.SpyObj<MatDialogRef<AddUserDialogComponent>>;
	let userMgmtRequestServiceSpy: jasmine.SpyObj<UserMgmtRequestService>;
	let orgMgmtRequestServiceSpy: jasmine.SpyObj<OrgMgmtRequestService>;

	beforeEach(async () => {
		userMgmtRequestServiceSpy = jasmine.createSpyObj('UserMgmtRequestService', ['getUserInfo', 'createUser', 'updateUser']);
		orgMgmtRequestServiceSpy = jasmine.createSpyObj('OrgMgmtRequestService', ['getOrgList']);

		mockDialogRef = jasmine.createSpyObj<MatDialogRef<AddUserDialogComponent>>('MatDialogRef', ['close']);
		await TestBed.configureTestingModule({
			imports: [AddUserDialogComponent, TranslateModule.forRoot()],
			providers: [
				{ provide: MatDialogRef, useValue: mockDialogRef },
				{ provide: MAT_DIALOG_DATA, useValue: {} },
				{ provide: UserMgmtRequestService, useValue: userMgmtRequestServiceSpy },
				{ provide: OrgMgmtRequestService, useValue: orgMgmtRequestServiceSpy },
			],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		}).compileComponents();

		orgMgmtRequestServiceSpy.getOrgList.and.returnValue(of([]));

		fixture = TestBed.createComponent(AddUserDialogComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should call dialogRef.close when onCancel is called', () => {
		component.onCancel();
		expect(mockDialogRef.close).toHaveBeenCalled();
	});

	it('should call dialogRef.close with no arguments', () => {
		component.onCancel();
		expect(mockDialogRef.close).toHaveBeenCalledWith();
	});

	it('should not call dialogRef.close if form is invalid when onOk is called', () => {
		component.addUserForm.markAllAsTouched();
		component.addUserForm.patchValue({ firstName: '', lastName: '', email: '', orgId: '', primaryOrgId: '', userType: '' });
		component.onOk();
		expect(mockDialogRef.close).not.toHaveBeenCalled();
	});

	it('should call createUser and dialogRef.close if form is valid and no userId in data', () => {
		const userPayload = {
			firstName: 'John',
			lastName: 'Doe',
			email: '<EMAIL>',
			orgId: '1',
			primaryOrgId: '1',
			userType: 'admin',
			secondaryOrgIds: [],
		};
		component.addUserForm.setValue({
			firstName: userPayload.firstName,
			lastName: userPayload.lastName,
			email: userPayload.email,
			orgId: userPayload.orgId,
			primaryOrgId: userPayload.primaryOrgId,
			userType: userPayload.userType,
		});
		userMgmtRequestServiceSpy.createUser.and.returnValue(of('success'));
		component.getSecondaryUserList = jasmine.createSpy().and.returnValue([]);
		component.onOk();
		expect(userMgmtRequestServiceSpy.createUser).toHaveBeenCalledWith(jasmine.objectContaining(userPayload));
		// simulate async
		fixture.detectChanges();
		expect(mockDialogRef.close).toHaveBeenCalled();
	});

	it('should call updateUser and dialogRef.close if form is valid and userId in data', () => {
		const userPayload = {
			firstName: 'Jane',
			lastName: 'Smith',
			email: '<EMAIL>',
			orgId: '2',
			primaryOrgId: '2',
			userType: 'user',
			secondaryOrgIds: [],
			userId: '123',
		};
		component.data.userId = '123';
		component.addUserForm.setValue({
			firstName: userPayload.firstName,
			lastName: userPayload.lastName,
			email: userPayload.email,
			orgId: userPayload.orgId,
			primaryOrgId: userPayload.primaryOrgId,
			userType: userPayload.userType,
		});
		userMgmtRequestServiceSpy.updateUser.and.returnValue(of('success'));
		component.getSecondaryUserList = jasmine.createSpy().and.returnValue([]);
		component.onOk();
		expect(userMgmtRequestServiceSpy.updateUser).toHaveBeenCalledWith(jasmine.objectContaining(userPayload));
		// simulate async
		fixture.detectChanges();
		expect(mockDialogRef.close).toHaveBeenCalled();
	});
});
