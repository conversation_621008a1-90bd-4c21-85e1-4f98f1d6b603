import { TestBed } from '@angular/core/testing';
import { UserMgmtRequestService } from './user-mgmt-request.service';
import { USER_LIST } from '../ref-data/user-list.data';
import { UserListObject } from '../models/user-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { HttpClient } from '@angular/common/http';
import { of } from 'rxjs';

describe('UserMgmtRequestService', () => {
	let service: UserMgmtRequestService;
	let httpClientSpy: jasmine.SpyObj<HttpClient>;

	beforeEach(() => {
		httpClientSpy = jasmine.createSpyObj('HttpClient', ['get']);

		TestBed.configureTestingModule({
			providers: [UserMgmtRequestService, { provide: HttpClient, useValue: httpClientSpy }],
		});

		service = TestBed.inject(UserMgmtRequestService);
	});

	describe('getUserList', () => {
		it('should retrieve all User list items', (done: DoneFn) => {
			// Arrange
			const keywords = '';
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<UserListObject> = {
				rows: USER_LIST,
				total: USER_LIST.length,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getUserList(paginationRequest, keywords).subscribe({
				next: (response: PaginationResponse<UserListObject>) => {
					// Assert
					expect(response.rows.length).toBe(USER_LIST.length);
					expect(response.total).toBe(USER_LIST.length);
					expect(response.rows[0].userId).toBe(USER_LIST[0].userId);
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith('/prod-api/user/user/list', { params: jasmine.any(Object) });
		});

		it('should apply search filters when provided', (done: DoneFn) => {
			// Arrange
			const keywords = 'test123';
			const paginationRequest = { pageNum: 1, pageSize: 10 };
			const mockResponse: PaginationResponse<UserListObject> = {
				rows: [USER_LIST[0]],
				total: 1,
			};

			httpClientSpy.get.and.returnValue(of(mockResponse));

			// Act
			service.getUserList(paginationRequest, keywords).subscribe({
				next: (response: PaginationResponse<UserListObject>) => {
					// Assert
					expect(response.rows.length).toBe(1);
					expect(response.rows[0].userId).toBe('2b7f9542-8e28-4d1f-b30b-ac0eac97db2d');
					expect(response.rows[0].firstName).toBe('John');
					done();
				},
				error: done.fail,
			});

			// Verify the API was called with correct parameters
			expect(httpClientSpy.get).toHaveBeenCalledWith('/prod-api/user/user/list', { params: jasmine.any(Object) });
		});
	});
});
