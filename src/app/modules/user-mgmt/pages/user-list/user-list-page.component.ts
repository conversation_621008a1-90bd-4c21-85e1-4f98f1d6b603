import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { UserTableComponent } from '../../components/user-table/user-table.component';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule } from '@ngx-translate/core';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { UserListObject } from '../../models/user-list-object.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { UserMgmtRequestService } from '../../services/user-mgmt-request.service';

@Component({
	selector: 'orll-user-list-page',
	templateUrl: './user-list-page.component.html',
	styleUrl: './user-list-page.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [UserTableComponent, MatIconModule, TranslateModule, SpinnerComponent],
})
export default class UserListPageComponent extends DestroyRefComponent implements OnInit {
	keyword = '';
	userList: UserListObject[] = [];
	userListTotalRecords = 0;
	dataLoading = false;
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};

	constructor(
		private readonly userMgmtRequestService: UserMgmtRequestService,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnInit(): void {
		this.getUserListPage(this.pageParams);
	}

	onSearch(keyword: string): void {
		this.keyword = keyword;
		this.getUserListPage(this.pageParams);
	}

	onSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
	}

	onPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}

		this.getUserListPage(this.pageParams);
	}

	private getUserListPage(pageParams: PaginationRequest): void {
		this.dataLoading = true;
		this.userList = [];

		this.userMgmtRequestService
			.getUserList(pageParams, this.keyword)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.userList = res.rows;
					this.userListTotalRecords = res.total;
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}
}
