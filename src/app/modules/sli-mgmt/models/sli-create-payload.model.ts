import { Dimensions } from './piece/dimensions.model';
import { Piece } from './piece/piece.model';
import { ShipmentParty } from './shipment-party.model';
import { Currency } from './currency.model';

export interface SliCreatePayload {
	shipmentParty: ShipmentParty[];
	departureLocation: string;
	arrivalLocation: string;
	shippingInfo: string;
	goodsDescription: string;
	totalGrossWeight: number;
	totalDimensions: Dimensions;
	insuredAmount: Currency;
	declaredValueForCarriage: Currency;
	declaredValueForCustoms: Currency;
	weightValuationIndicator: string;
	textualHandlingInstructions: string;
	incoterms: string;
	pieces: Piece[];
}
