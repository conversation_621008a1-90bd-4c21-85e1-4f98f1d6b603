import { ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
// eslint-disable-next-line @typescript-eslint/naming-convention
import PieceAddPageComponent from './piece-add-page.component';
import { Router, RouterModule } from '@angular/router';
import { HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { By } from '@angular/platform-browser';
import { SliPieceFormComponent } from '../../components/sli-piece-form/sli-piece-form.component';
import { SliPieceItemComponent } from '../../components/sli-piece-item/sli-piece-item.component';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';
import { ChangeDetectorRef } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { NotificationService } from '@shared/services/notification.service';
import { Observable, of, throwError } from 'rxjs';
import { Piece } from '../../models/piece/piece.model';

describe('PieceAddPageComponent', () => {
	let component: PieceAddPageComponent;
	let fixture: ComponentFixture<PieceAddPageComponent>;
	let httpClient: HttpClient;

	// Mocks
	let mockRouter: jasmine.SpyObj<Router>;
	let mockSliCreateRequestService: jasmine.SpyObj<SliCreateRequestService>;
	let mockNotificationService: jasmine.SpyObj<NotificationService>;
	let mockDialog: jasmine.SpyObj<MatDialog>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;

	beforeEach(async () => {
		// Create spies
		mockRouter = jasmine.createSpyObj('Router', ['navigate']);
		mockSliCreateRequestService = jasmine.createSpyObj('SliCreateRequestService', [
			'createPiece',
			'updatePiece',
			'getPieceDetail',
			'getPackingTypes',
		]);
		mockNotificationService = jasmine.createSpyObj('NotificationService', ['showError', 'showSuccess']);
		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);
		mockChangeDetectorRef = jasmine.createSpyObj('ChangeDetectorRef', ['markForCheck']);

		// Configure mock responses
		mockSliCreateRequestService.getPackingTypes.and.returnValue(of([]));
		mockDialog.open.and.returnValue({
			afterClosed: () => of(true),
		} as any);

		await TestBed.configureTestingModule({
			imports: [
				PieceAddPageComponent,
				TranslateModule.forRoot(),
				ReactiveFormsModule,
				NoopAnimationsModule,
				MatFormFieldModule,
				MatInputModule,
				MatButtonModule,
				MatIconModule,
				SliPieceFormComponent,
				SliPieceItemComponent,
				RouterModule.forRoot([], {
					useHash: true,
					bindToComponentInputs: true,
				}),
			],
			providers: [
				{ provide: Router, useValue: mockRouter },
				{ provide: SliCreateRequestService, useValue: mockSliCreateRequestService },
				{ provide: NotificationService, useValue: mockNotificationService },
				{ provide: MatDialog, useValue: mockDialog },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(PieceAddPageComponent);
		component = fixture.componentInstance;
		httpClient = TestBed.inject(HttpClient);

		// Set required inputs
		component.pieceType = 'general';
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize with default values', async () => {
		component.pieceType = 'general';
		fixture.detectChanges();

		// Check default values
		expect(component.pieceType).toBe('general');
		expect(component.pieceQuantity.value).toBe(1);
	});

	it('should validate pieceQuantity with positive number pattern', () => {
		// Invalid values
		component.pieceQuantity.setValue(Number('abc'));
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		component.pieceQuantity.setValue(0); // Not positive
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		component.pieceQuantity.setValue(-1); // Negative
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		component.pieceQuantity.setValue(1.5); // Decimal
		expect(component.pieceQuantity.valid).toBeFalsy();
		expect(component.pieceQuantity.hasError('pattern')).toBeTruthy();

		// Valid values
		component.pieceQuantity.setValue(1);
		expect(component.pieceQuantity.valid).toBeTruthy();

		component.pieceQuantity.setValue(10);
		expect(component.pieceQuantity.valid).toBeTruthy();
	});

	it('should show error message when pieceQuantity is invalid', () => {
		component.pieceType = 'general';

		fixture.detectChanges();

		// Set invalid value
		component.pieceQuantity.setValue(null);
		component.pieceQuantity.markAsTouched();
		fixture.detectChanges();

		// Check for required error
		const requiredError = fixture.debugElement.query(By.css('mat-error'));
		expect(requiredError).toBeTruthy();

		// Set invalid pattern
		component.pieceQuantity.setValue(0);
		fixture.detectChanges();

		// Check for pattern error
		const patternError = fixture.debugElement.query(By.css('mat-error'));
		expect(patternError).toBeTruthy();
	});

	it('should have cancel and done buttons', () => {
		component.pieceType = 'general';

		fixture.detectChanges();

		// Find buttons specifically in the footer section (not including child component buttons)
		const footerButtons = fixture.debugElement.queryAll(By.css('.orll-piece-add-page__buttons button'));
		expect(footerButtons.length).toBe(2);

		// Check cancel button
		const cancelButton = fixture.debugElement.query(By.css('.orll-piece-add-page__cancel-button'));
		expect(cancelButton).toBeTruthy();

		// Check done button
		const doneButton = fixture.debugElement.query(By.css('.orll-piece-add-page__buttons button[color="primary"]'));
		expect(doneButton).toBeTruthy();
	});

	it('should call onCancel when cancel button is clicked', () => {
		component.pieceType = 'general';

		fixture.detectChanges();

		// Spy on onCancel method
		spyOn(component, 'onCancel');

		// Find and click cancel button
		const cancelButton = fixture.debugElement.query(By.css('.orll-piece-add-page__cancel-button'));
		cancelButton.triggerEventHandler('click', null);

		// Check if onCancel was called
		expect(component.onCancel).toHaveBeenCalled();
	});

	it('should call onDone when done button is clicked', () => {
		component.pieceType = 'general';

		fixture.detectChanges();

		// Spy on onDone method
		spyOn(component, 'onDone');

		// Find and click done button
		const doneButton = fixture.debugElement.query(By.css('.orll-piece-add-page__done-button'));
		doneButton.triggerEventHandler('click', null);

		// Check if onDone was called
		expect(component.onDone).toHaveBeenCalled();
	});

	it('should test create dg piece', () => {
		// Set component type
		component.pieceType = 'dg';

		fixture.detectChanges();

		// Create a mock DG component
		component.dgPieceComponent = jasmine.createSpyObj('DgPieceAddComponent', [], {
			sliDgPieceForm: jasmine.createSpyObj('FormGroup', ['markAllAsTouched', 'patchValue'], {
				invalid: false,
				value: {
					productDescription: 'test productDescription',
					typeOfPackage: {
						typeCode: '1A',
						description: 'Drum, steel',
					},
					packagedIdentifier: 'test packagedIdentifier',
					whetherHaveDeclaredValueForCustoms: true,
					whetherHaveDeclaredValueForCarriage: true,
					specialProvisionId: 'test specialProvisionId',
					explosiveCompatibilityGroupCode: 'test explosiveCompatibilityGroupCode',
					packagingDangerLevelCode: 'test packagingDangerLevelCode',
					technicalName: 'test technicalName',
					unNumber: 'test unNumber',
					shippersDeclaration: 'test shippersDeclaration',
					handlingInformation: 'test handlingInformation',
					allPackedInOne: true,
					qValueNumeric: 111,
					upid: 'test upid',
					shippingMarks: 'test shippingMarks',
					grossWeight: 0,
					dimensions: {
						length: 0,
						width: 0,
						height: 0,
					},
					hsCommodityDescription: 'test hsCommodityDescription',
					properShippingName: 'test properShippingName',
					textualHandlingInstructions: 'test textualHandlingInstructions',
					hazardClassificationId: 'test hazardClassificationId',
					additionalHazardClassificationId: 'test additionalHazardClassificationId',
					packingInstructionNumber: 'test packingInstructionNumber',
					complianceDeclaration: 'test complianceDeclaration',
					exclusiveUseIndicator: 'test exclusiveUseIndicator',
					authorizationInformation: 'test authorizationInformation',
					aircraftLimitationInformation: 'test aircraftLimitationInformation',
				},
			}),
			dgPieceItems: [],
		});

		// Spy on HttpClient.post
		const httpPostSpy = spyOn(httpClient, 'post').and.returnValue(of({ success: true }));

		// Execute the method
		component.onDone();

		// Verify
		expect(httpPostSpy).toHaveBeenCalledWith(
			'/prod-api/sli/dgPiece',
			jasmine.objectContaining({
				type: 'PieceDg',
				packagingType: { typeCode: '1A', description: 'Drum, steel' },
				nvdForCustoms: true,
				nvdForCarriage: true,
				grossWeight: 0,
				upid: 'test upid',
				dimensions: { length: 0, width: 0, height: 0 },
				packagedIdentifier: 'test packagedIdentifier',
				textualHandlingInstructions: 'test textualHandlingInstructions',
				shippingMarks: 'test shippingMarks',
				pieceQuantity: 1,
				sliNumber: '',
				productDg: {
					specialProvisionId: 'test specialProvisionId',
					explosiveCompatibilityGroupCode: 'test explosiveCompatibilityGroupCode',
					packagingDangerLevelCode: 'test packagingDangerLevelCode',
					technicalName: 'test technicalName',
					unNumber: 'test unNumber',
					description: 'test productDescription',
					hsCommodityDescription: 'test hsCommodityDescription',
					properShippingName: 'test properShippingName',
					hazardClassificationId: 'test hazardClassificationId',
					additionalHazardClassificationId: 'test additionalHazardClassificationId',
					packingInstructionNumber: 'test packingInstructionNumber',
					authorizationInformation: 'test authorizationInformation',
				},
				allPackedInOneIndicator: true,
				dgDeclaration: {
					shipperDeclarationText: 'test shippersDeclaration',
					handlingInformation: 'test handlingInformation',
					complianceDeclarationText: 'test complianceDeclaration',
					exclusiveUseIndicator: 'test exclusiveUseIndicator',
					aircraftLimitationInformation: 'test aircraftLimitationInformation',
				},
				containedItems: [],
				qvalueNumeric: 111,
			})
		);
	});

	it('should create live animal piece', () => {
		expect(httpClient).toBeInstanceOf(HttpClient);

		// Set component type to live animal
		component.pieceType = 'la';

		// Setup mock component and spies
		component.liveAnimalPieceComponent = jasmine.createSpyObj('LiveAnimalPieceAddComponent', [], {
			sliLiveAnimalPieceForm: jasmine.createSpyObj('FormGroup', ['markAllAsTouched'], {
				invalid: false,
				value: {
					productDescription: 'test productDescription',
					typeOfPackage: {
						typeCode: '1A',
						description: 'Drum, steel',
					},
					packagedIdentifier: 'test packagedIdentifier',
					speciesCommonName: 'test speciesCommonName',
					speciesScientificName: 'test speciesScientificName',
					specimenDescription: 'test specimenDescription',
					animalQuantity: 1,
					shippingMarks: 'test shippingMarks',
					upid: 'test upid',
					grossWeight: 0,
					dimensions: {
						length: 0,
						width: 0,
						height: 0,
					},
					whetherHaveDeclaredValueForCustoms: false,
					whetherHaveDeclaredValueForCarriage: false,
					textualHandlingInstructions: 'test textualHandlingInstructions',
				},
			}),
		});

		// Spy on HttpClient.post
		spyOn(httpClient, 'post').and.returnValue(of({ success: true }));

		// Execute the method
		component.onDone();

		// Verify the HTTP request was made correctly
		expect(httpClient.post).toHaveBeenCalledWith(
			'/prod-api/sli/laPiece',
			jasmine.objectContaining({
				type: 'PieceLiveAnimals',
				packagingType: { typeCode: '1A', description: 'Drum, steel' },
				nvdForCustoms: false,
				nvdForCarriage: false,
				grossWeight: 0,
				upid: 'test upid',
				dimensions: { length: 0, width: 0, height: 0 },
				packagedIdentifier: 'test packagedIdentifier',
				textualHandlingInstructions: 'test textualHandlingInstructions',
				shippingMarks: 'test shippingMarks',
				pieceQuantity: 1,
				sliNumber: '',
				slac: 0,
				product: { description: 'test productDescription', hsCommodityDescription: '' },
				quantityAnimals: 1,
				speciesCommonName: 'test speciesCommonName',
				speciesScientificName: 'test speciesScientificName',
				specimenDescription: 'test specimenDescription',
			})
		);
	});

	describe('getPieceDetail', () => {
		it('should call sliCreateRequestService.getPieceDetail with the provided pieceId', () => {
			// Setup
			const pieceId = 'test-piece-id';
			const mockPiece: Piece = {
				pieceId,
				upid: 'TEST123',
				type: 'Piece',
				product: { description: 'Test Product' },
				packagingType: { typeCode: 'BOX', description: 'Box' },
				packagedIdentifier: 'ID123',
				nvdForCustoms: true,
				nvdForCarriage: true,
				grossWeight: 50,
				dimensions: { length: 20, width: 15, height: 10 },
				pieceQuantity: 1,
				slac: 0,
				shippingMarks: '',
				textualHandlingInstructions: '',
				containedPieces: [],
				containedItems: [],
			} as Piece;

			mockSliCreateRequestService.getPieceDetail.and.returnValue(of(mockPiece));

			// Execute
			component.getPieceDetail(pieceId);

			// Verify
			expect(mockSliCreateRequestService.getPieceDetail).toHaveBeenCalledWith(pieceId);
			expect(component.piece).toEqual(mockPiece);
			expect(component.pieceQuantity?.value).toEqual(mockPiece.pieceQuantity);
		});
	});

	describe('pieceDetailRequest', () => {
		it('should call createPiece when pieceId is not provided', () => {
			// Setup
			component.sliNumber = 'test-sli';
			const mockPiece: Piece = {
				upid: 'TEST123',
				type: 'Piece',
				product: { description: 'Test Product' },
				packagingType: { typeCode: 'BOX', description: 'Box' },
				packagedIdentifier: 'ID123',
				nvdForCustoms: true,
				nvdForCarriage: true,
				grossWeight: 50,
				dimensions: { length: 20, width: 15, height: 10 },
				pieceQuantity: 1,
				slac: 0,
				shippingMarks: '',
				textualHandlingInstructions: '',
				containedPieces: [],
				containedItems: [],
				sliNumber: component.sliNumber,
			} as Piece;

			mockSliCreateRequestService.createPiece.and.returnValue(of('piece-123'));

			// Execute
			let result: string | undefined;
			component['pieceDetailRequest'](mockPiece).subscribe((res) => {
				result = res;
			});

			// Verify
			expect(mockSliCreateRequestService.createPiece).toHaveBeenCalledWith(mockPiece);
			expect(mockSliCreateRequestService.updatePiece).not.toHaveBeenCalled();
			expect(result).toBe('piece-123');
		});

		it('should call createPiece when sliNumber equals pieceId', () => {
			// Setup
			component.sliNumber = 'test-sli';
			component.pieceId = 'test-sli';
			const mockPiece: Piece = {
				pieceId: component.pieceId,
				upid: 'TEST123',
				type: 'Piece',
				product: { description: 'Test Product' },
				packagingType: { typeCode: 'BOX', description: 'Box' },
				packagedIdentifier: 'ID123',
				nvdForCustoms: true,
				nvdForCarriage: true,
				grossWeight: 50,
				dimensions: { length: 20, width: 15, height: 10 },
				pieceQuantity: 1,
				slac: 0,
				shippingMarks: '',
				textualHandlingInstructions: '',
				containedPieces: [],
				containedItems: [],
				sliNumber: component.sliNumber,
			} as Piece;

			mockSliCreateRequestService.createPiece.and.returnValue(of('piece-123'));

			// Execute
			let result: string | undefined;
			component['pieceDetailRequest'](mockPiece).subscribe((res) => {
				result = res;
			});

			// Verify
			expect(mockSliCreateRequestService.createPiece).toHaveBeenCalledWith(mockPiece);
			expect(mockSliCreateRequestService.updatePiece).not.toHaveBeenCalled();
			expect(result).toBe('piece-123');
		});

		it('should call updatePiece when sliNumber does not equal pieceId', () => {
			// Setup
			component.sliNumber = 'test-sli';
			component.pieceId = 'test-piece';
			const mockPiece: Piece = {
				pieceId: component.pieceId,
				upid: 'TEST123',
				type: 'Piece',
				product: { description: 'Test Product' },
				packagingType: { typeCode: 'BOX', description: 'Box' },
				packagedIdentifier: 'ID123',
				nvdForCustoms: true,
				nvdForCarriage: true,
				grossWeight: 50,
				dimensions: { length: 20, width: 15, height: 10 },
				pieceQuantity: 1,
				slac: 0,
				shippingMarks: '',
				textualHandlingInstructions: '',
				containedPieces: [],
				containedItems: [],
				sliNumber: component.sliNumber,
			} as Piece;

			mockSliCreateRequestService.updatePiece.and.returnValue(of('piece-updated'));

			// Execute
			let result: string | undefined;
			component['pieceDetailRequest'](mockPiece).subscribe((res) => {
				result = res;
			});

			// Verify
			expect(mockSliCreateRequestService.updatePiece).toHaveBeenCalledWith(mockPiece, component.pieceId);
			expect(mockSliCreateRequestService.createPiece).not.toHaveBeenCalled();
			expect(result).toBe('piece-updated');
		});
	});

	describe('canDeactivate', () => {
		it('should return true when form is saved', () => {
			// Setup
			component.isSaved = true;

			// Execute & Verify
			expect(component.canDeactivate()).toBe(true);
			expect(mockDialog.open).not.toHaveBeenCalled();
		});

		it('should navigate and return true when dialog is confirmed', fakeAsync(() => {
			// Setup
			component.sliNumber = 'test-sli';
			component.isSaved = false;
			component.isConfirmed = false;
			mockDialog.open.and.returnValue({
				afterClosed: () => of(true),
			} as any);

			// Execute
			let result: boolean | null = null;
			(component.canDeactivate() as Observable<boolean>).subscribe((value) => {
				result = value;
			});
			tick();

			// Verify
			expect(component.isConfirmed).toBe(true);
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli/edit', 'test-sli']);
			expect(result).toBeTrue();
		}));

		it('should not navigate and return false when dialog is cancelled', fakeAsync(() => {
			// Setup
			component.isSaved = false;
			component.isConfirmed = false;
			mockDialog.open.and.returnValue({
				afterClosed: () => of(false),
			} as any);

			// Execute
			let result: boolean | null = null;
			(component.canDeactivate() as Observable<boolean>).subscribe((value) => {
				result = value;
			});
			tick();

			// Verify
			expect(component.isConfirmed).toBe(false);
			expect(mockRouter.navigate).not.toHaveBeenCalled();
			expect(result).toBeFalse();
		}));
	});

	describe('onCancel', () => {
		it('should open confirmation dialog when clicked', () => {
			// Setup
			mockDialog.open.and.returnValue({
				afterClosed: () =>
					new Observable(() => {
						// Never complete this observable to simulate dialog still being open
					}),
			} as any);

			// Execute
			component.onCancel();

			// Verify
			expect(mockDialog.open).toHaveBeenCalled();
			expect(mockRouter.navigate).not.toHaveBeenCalled();
		});

		it('should navigate when confirmation dialog returns true', () => {
			// Setup
			component.sliNumber = 'test-sli';
			mockDialog.open.and.returnValue({
				afterClosed: () => of(true),
			} as any);

			// Execute
			component.onCancel();

			// Verify
			expect(component.isConfirmed).toBe(true);
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli/edit', 'test-sli']);
		});

		it('should not navigate when confirmation dialog returns false', () => {
			// Setup
			mockDialog.open.and.returnValue({
				afterClosed: () => of(false),
			} as any);

			// Execute
			component.onCancel();

			// Verify
			expect(component.isConfirmed).toBe(false);
			expect(mockRouter.navigate).not.toHaveBeenCalled();
		});
	});

	describe('addOrUpdatePiece', () => {
		beforeEach(() => {
			// Setup mock components
			component.gelPiece = jasmine.createSpyObj('SliPieceFormComponent', ['getFormData'], {
				sliPieceForm: jasmine.createSpyObj('FormGroup', ['markAllAsTouched']),
			});
			component.pieceQuantity.markAsTouched();
			component.pieceItemWrapper = jasmine.createSpyObj('SliPieceItemComponent', ['getPieceItemList', 'getPieceInList']);

			component.sliNumber = 'test-sli';
			(component.gelPiece.getFormData as jasmine.Spy).and.returnValue({ upid: 'test-upid' });
			(component.pieceItemWrapper.getPieceItemList as jasmine.Spy).and.returnValue([]);
			(component.pieceItemWrapper.getPieceInList as jasmine.Spy).and.returnValue([]);
		});

		it('should mark forms as touched and validate inputs', () => {
			// Setup
			mockSliCreateRequestService.createPiece.and.returnValue(of('piece-123'));
			spyOn<any>(component, 'pieceDetailRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(component.gelPiece.sliPieceForm?.markAllAsTouched).toHaveBeenCalled();
		});

		it('should show dialog if form validation fails', () => {
			// Setup
			component.sliNumber = '';

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(mockDialog.open).toHaveBeenCalled();
			expect(mockSliCreateRequestService.createPiece).not.toHaveBeenCalled();
		});

		it('should call pieceDetailRequest with correct payload', () => {
			// Setup
			const expectedPayload = {
				sliNumber: 'test-sli',
				upid: 'test-upid',
				slac: 0,
				pieceQuantity: 1,
				containedItems: [],
				containedPieces: [],
			};
			component.pieceQuantity.setValue(1);

			spyOn<any>(component, 'pieceDetailRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(component['pieceDetailRequest']).toHaveBeenCalledWith(jasmine.objectContaining(expectedPayload));
		});

		it('should set dataLoading to true while processing', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(component.dataLoading).toBe(false); // Should be reset after observable completes
		});

		it('should navigate after successful creation/update', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailRequest').and.returnValue(of('piece-123'));

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(component.isSaved).toBe(true);
			expect(mockRouter.navigate).toHaveBeenCalledWith(['sli/edit', 'test-sli']);
		});

		it('should handle errors and reset dataLoading', () => {
			// Setup
			spyOn<any>(component, 'pieceDetailRequest').and.returnValue(throwError(() => new Error('API error')));

			// Execute
			component['addOrUpdatePiece']();

			// Verify
			expect(component.dataLoading).toBe(false);
			expect(component.isSaved).toBe(false);
		});
	});

	describe('_addDgPiece', () => {
		beforeEach(() => {
			// Setup mock DG component
			component.pieceType = 'dg';
			component.dgPieceComponent = jasmine.createSpyObj('DgPieceAddComponent', [], {
				sliDgPieceForm: jasmine.createSpyObj('FormGroup', ['markAllAsTouched'], {
					invalid: false,
					value: {
						productDescription: 'test productDescription',
						typeOfPackage: {
							typeCode: '1A',
							description: 'Drum, steel',
						},
						packagedIdentifier: 'test packagedIdentifier',
						whetherHaveDeclaredValueForCustoms: true,
						whetherHaveDeclaredValueForCarriage: true,
						specialProvisionId: 'test specialProvisionId',
						explosiveCompatibilityGroupCode: 'test explosiveCompatibilityGroupCode',
						packagingDangerLevelCode: 'test packagingDangerLevelCode',
						technicalName: 'test technicalName',
						unNumber: 'test unNumber',
						shippersDeclaration: 'test shippersDeclaration',
						handlingInformation: 'test handlingInformation',
						allPackedInOne: true,
						qValueNumeric: 111,
						upid: 'test upid',
						shippingMarks: 'test shippingMarks',
						grossWeight: '111.22',
						dimensions: {
							length: 0,
							width: 0,
							height: 0,
						},
						hsCommodityDescription: 'test hsCommodityDescription',
						properShippingName: 'test properShippingName',
						textualHandlingInstructions: 'test textualHandlingInstructions',
						hazardClassificationId: 'test hazardClassificationId',
						additionalHazardClassificationId: 'test additionalHazardClassificationId',
						packingInstructionNumber: 'test packingInstructionNumber',
						complianceDeclaration: 'test complianceDeclaration',
						exclusiveUseIndicator: 'test exclusiveUseIndicator',
						authorizationInformation: 'test authorizationInformation',
						aircraftLimitationInformation: 'test aircraftLimitationInformation',
					},
				}),
				dgPieceItems: [],
			});
		});

		it('should validate the DG form before submission', () => {
			// Override the method to directly test notification service call
			spyOn<any>(component, 'addDgPiece').and.callThrough();

			// Setup form invalid
			Object.defineProperty(component.dgPieceComponent!.sliDgPieceForm, 'invalid', { get: () => true });

			// Reset notification service spy
			mockNotificationService.showError.calls.reset();

			// Call the method directly
			component['addDgPiece']();

			// Verify
			expect(component.dgPieceComponent!.sliDgPieceForm.markAllAsTouched).toHaveBeenCalled();
			expect(mockNotificationService.showError).toHaveBeenCalledWith('DG piece form is not valid');
		});

		it('should make HTTP request with correct payload for DG piece', () => {
			// Setup - create a spy for httpClient.post
			spyOn(httpClient, 'post').and.returnValue(of({ success: true }));

			// Execute
			component['addDgPiece']();

			// Verify
			expect(httpClient.post).toHaveBeenCalledWith(
				'/prod-api/sli/dgPiece',
				jasmine.objectContaining({
					type: 'PieceDg',
					packagingType: { typeCode: '1A', description: 'Drum, steel' },
					nvdForCustoms: true,
					nvdForCarriage: true,
					grossWeight: 111.22,
					upid: 'test upid',
					dimensions: { length: 0, width: 0, height: 0 },
					packagedIdentifier: 'test packagedIdentifier',
					textualHandlingInstructions: 'test textualHandlingInstructions',
					shippingMarks: 'test shippingMarks',
					pieceQuantity: 1,
				})
			);
		});

		it('should show error when DG component is not found', () => {
			// Setup
			component.dgPieceComponent = null;

			// Execute
			component['addDgPiece']();

			// Verify
			expect(mockNotificationService.showError).toHaveBeenCalledWith('DG piece component not found');
		});
	});

	describe('addLiveAnimalPiece', () => {
		beforeEach(() => {
			// Setup mock Live Animal component
			component.pieceType = 'la';

			component.liveAnimalPieceComponent = jasmine.createSpyObj('LiveAnimalPieceAddComponent', [], {
				sliLiveAnimalPieceForm: jasmine.createSpyObj('FormGroup', ['markAllAsTouched'], {
					invalid: false,
					value: {
						productDescription: 'test productDescription',
						typeOfPackage: {
							typeCode: '1A',
							description: 'Drum, steel',
						},
						packagedIdentifier: 'test packagedIdentifier',
						speciesCommonName: 'test speciesCommonName',
						speciesScientificName: 'test speciesScientificName',
						specimenDescription: 'test specimenDescription',
						animalQuantity: 1,
						shippingMarks: 'test shippingMarks',
						upid: 'test upid',
						grossWeight: 0,
						dimensions: {
							length: 0,
							width: 0,
							height: 0,
						},
						whetherHaveDeclaredValueForCustoms: false,
						whetherHaveDeclaredValueForCarriage: false,
						textualHandlingInstructions: 'test textualHandlingInstructions',
					},
				}),
			});
		});

		it('should validate the Live Animal form before submission', () => {
			// Override the method to directly test notification service call
			spyOn<any>(component, 'addLiveAnimalPiece').and.callThrough();

			// Setup form invalid
			Object.defineProperty(component.liveAnimalPieceComponent!.sliLiveAnimalPieceForm, 'invalid', { get: () => true });

			// Reset notification service spy
			mockNotificationService.showError.calls.reset();

			// Call the method directly
			component['addLiveAnimalPiece']();

			// Verify
			expect(component.liveAnimalPieceComponent!.sliLiveAnimalPieceForm.markAllAsTouched).toHaveBeenCalled();
			expect(mockNotificationService.showError).toHaveBeenCalledWith('Live animal piece form is not valid');
		});

		it('should make HTTP request with correct payload for Live Animal piece', () => {
			// Setup - create a spy for httpClient.post
			spyOn(httpClient, 'post').and.returnValue(of({ success: true }));

			// Execute
			component['addLiveAnimalPiece']();

			// Verify
			expect(httpClient.post).toHaveBeenCalledWith(
				'/prod-api/sli/laPiece',
				jasmine.objectContaining({
					type: 'PieceLiveAnimals',
					packagingType: { typeCode: '1A', description: 'Drum, steel' },
					nvdForCustoms: false,
					nvdForCarriage: false,
					grossWeight: 0,
					upid: 'test upid',
					dimensions: { length: 0, width: 0, height: 0 },
					packagedIdentifier: 'test packagedIdentifier',
					textualHandlingInstructions: 'test textualHandlingInstructions',
					shippingMarks: 'test shippingMarks',
					pieceQuantity: 1,
				})
			);
		});

		it('should show error when Live Animal component is not found', () => {
			// Setup
			component.liveAnimalPieceComponent = null;

			// Execute
			component['addLiveAnimalPiece']();

			// Verify
			expect(mockNotificationService.showError).toHaveBeenCalledWith('live animal piece component not found');
		});
	});
});
