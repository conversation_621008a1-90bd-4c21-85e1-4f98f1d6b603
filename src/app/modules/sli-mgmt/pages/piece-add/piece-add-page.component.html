<div class="orll-piece-add-page">
	<div class="iata-box orll-piece-add-page__box">
		@if (pieceType === 'general') {
			<orll-sli-piece-form [piece]="piece"></orll-sli-piece-form>
			<orll-sli-piece-item [pieceType]="pieceType" [piece]="piece"></orll-sli-piece-item>
		}
		@if (pieceType === 'dg') {
			<orll-dg-piece-add [piece]="piece"></orll-dg-piece-add>
		}
		@if (pieceType === 'la') {
			<orll-live-animal-piece-add [piece]="piece"></orll-live-animal-piece-add>
		}
	</div>
	<div class="orll-piece-add-page__footer">
		<div class="orll-piece-add-page__piece-quantity col-12">
			<mat-icon class="close-icon">close</mat-icon>
			<mat-form-field appearance="outline" class="field" floatLabel="always">
				<mat-label>{{'sli.piece.pieceQuantity' | translate}}</mat-label>
				<input matInput type="number" [formControl]="pieceQuantity" placeholder="{{'sli.piece.pieceQuantity' | translate}}">
				@if (pieceQuantity?.hasError('required')) {
					<mat-error>{{'sli.piece.pieceQuantity.required' | translate}}</mat-error>
				}
				@if (pieceQuantity?.hasError('pattern')) {
					<mat-error>{{'sli.mgmt.pieceList.pattern.positiveNumber' | translate}}</mat-error>
				}
			</mat-form-field>
		</div>

		<div class="orll-piece-add-page__buttons col-12">
			<button mat-stroked-button color="primary" (click)="onCancel()" class="orll-piece-add-page__cancel-button">
				{{'sli.mgmt.cancel' | translate}}
			</button>
			<button mat-flat-button color="primary" (click)="onDone()" class="orll-piece-add-page__done-button">
				<mat-icon>check</mat-icon>
				{{'sli.piece.done' | translate}}
			</button>
		</div>
	</div>
</div>
