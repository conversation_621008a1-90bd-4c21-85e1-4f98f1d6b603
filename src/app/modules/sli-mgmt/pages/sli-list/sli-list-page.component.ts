import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { SliListObject } from '../../models/sli-list-object.model';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { SliSearchRequestService } from '../../services/sli-search-request.service';
import { PageEvent } from '@angular/material/paginator';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { SliSearchComponent } from '../../components/sli-search/sli-search.component';
import { SliTableComponent } from '../../components/sli-table/sli-table.component';
import { SpinnerComponent } from '@shared/components/spinner/spinner.component';
import { SliSearchPayload } from '../../models/sli-search-payload.model';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { Sort } from '@angular/material/sort';
import { Observable } from 'rxjs';
import { PaginationResponse } from '@shared/models/pagination-response.model';

@Component({
	selector: 'orll-sli-list-page',
	templateUrl: './sli-list-page.component.html',
	styleUrls: ['./sli-list-page.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [SliSearchComponent, SliTableComponent, MatIconModule, TranslateModule, SpinnerComponent],
})
export default class SliListPageComponent extends DestroyRefComponent implements OnInit {
	@Input() fromCreateHawb = false;

	sliSearchPayload!: SliSearchPayload;
	sliList: SliListObject[] = [];
	sliListTotalRecords = 0;
	dataLoading = false;
	pageParams: PaginationRequest = {
		pageNum: 1,
		pageSize: 10,
	};

	constructor(
		private readonly sliSearchRequestService: SliSearchRequestService,
		private readonly cdr: ChangeDetectorRef
	) {
		super();
	}

	ngOnInit(): void {
		this.getSliListPage(this.pageParams);
	}

	onSearch(sliSearchPayload: SliSearchPayload): void {
		this.sliSearchPayload = sliSearchPayload;
		this.getSliListPage(this.pageParams);
	}

	onSliListSortChange(sort: Sort): void {
		if (sort.direction === '') {
			this.pageParams.orderByColumn = '';
			this.pageParams.isAsc = '';
			return;
		}
		this.pageParams.orderByColumn = sort.active;
		this.pageParams.isAsc = sort.direction;
	}

	onSliListPageChange(event: PageEvent & { sortField?: string; sortDirection?: string }): void {
		this.pageParams.pageNum = event.pageIndex + 1;
		this.pageParams.pageSize = event.pageSize;
		if (event.sortDirection) {
			this.pageParams.orderByColumn = event.sortField;
			this.pageParams.isAsc = event.sortDirection;
		}

		this.getSliListPage(this.pageParams);
	}

	private sliSearchRequest(
		pageParams: PaginationRequest,
		sliSearchPayload: SliSearchPayload
	): Observable<PaginationResponse<SliListObject>> {
		if (this.fromCreateHawb) {
			return this.sliSearchRequestService.getSharedSliList(pageParams, sliSearchPayload);
		} else {
			return this.sliSearchRequestService.getSliList(pageParams, sliSearchPayload);
		}
	}

	private getSliListPage(pageParams: PaginationRequest): void {
		this.dataLoading = true;
		this.sliList = [];

		this.sliSearchRequest(pageParams, this.sliSearchPayload)
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe({
				next: (res) => {
					this.sliList = res.rows;
					this.sliListTotalRecords = res.total;
					this.dataLoading = false;
					this.cdr.markForCheck();
				},
				error: () => {
					this.dataLoading = false;
				},
			});
	}
}
