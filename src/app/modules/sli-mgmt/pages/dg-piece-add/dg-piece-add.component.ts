import { ChangeDetectionStrategy, Component, Input, OnInit, SimpleChanges, OnChanges } from '@angular/core';
import { AbstractControl, FormsModule, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { TranslatePipe } from '@ngx-translate/core';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { PieceItemPanelComponent } from '../../components/piece-item-panel/piece-item-panel.component';
import { PieceItem } from '../../models/piece/piece-item.model';
import { CodeName } from '@shared/models/code-name.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { Piece } from '../../models/piece/piece.model';

const REGEX_ONE_NUMBER = /^\d+(\.\d{1})?$/

@Component({
	selector: 'orll-dg-piece-add',
	imports: [
		CommonModule,
		ReactiveFormsModule,
		TranslatePipe,
		MatFormFieldModule,
		MatIconModule,
		MatInputModule,
		MatExpansionModule,
		MatButtonModule,
		MatSelectModule,
		PieceItemPanelComponent,
		MatAutocompleteModule,
		FormsModule,
	],
	templateUrl: './dg-piece-add.component.html',
	changeDetection: ChangeDetectionStrategy.OnPush,
	styleUrl: './dg-piece-add.component.scss',
})
export class DgPieceAddComponent implements OnInit, OnChanges {
	@Input() piece: Piece | null = null;

	typeOfPackage = '';

	sliDgPieceForm = this.fb.group({
		productDescription: ['', Validators.required],
		typeOfPackage: this.fb.group({
			typeCode: [''],
			description: [''],
		}),
		packagedIdentifier: [], //optional
		whetherHaveDeclaredValueForCustoms: [true],
		whetherHaveDeclaredValueForCarriage: [true],
		specialProvisionId: [],
		explosiveCompatibilityGroupCode: [],
		packagingDangerLevelCode: [],
		technicalName: [],
		unNumber: [],
		shippersDeclaration: [],
		handlingInformation: [],
		allPackedInOne: [true], // optional
		qValueNumeric: [
			0,
			[
				(control: AbstractControl) => {
					if (this.sliDgPieceForm?.get('allPackedInOne')?.value === true) {
						return Validators.required(control);
					}
					return null;
				},
			],
		],
		upid: [], //optional
		shippingMarks: [], //optional
		grossWeight: this.fb.control<string | null>(null, [Validators.pattern(REGEX_ONE_NUMBER)]),
		dimensions: this.fb.group({
			length: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
			width: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
			height: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
		}),
		hsCommodityDescription: [],
		properShippingName: [],
		textualHandlingInstructions: [],
		hazardClassificationId: [],
		additionalHazardClassificationId: [],
		packingInstructionNumber: [],
		complianceDeclaration: [],
		exclusiveUseIndicator: [],
		authorizationInformation: [],
		aircraftLimitationInformation: [],
	});

	dgPieceItems: PieceItem[] = [];

	packagingTypes: CodeName[] = [];
	filteredPackagingTypes: CodeName[] = [];

	constructor(
		private readonly fb: NonNullableFormBuilder,
		public readonly sliCreateRequestService: SliCreateRequestService
	) {}

	ngOnInit() {
		this.sliCreateRequestService.getPackingTypes().subscribe((packagingTypes: CodeName[]) => {
			this.packagingTypes = packagingTypes;
			this.filteredPackagingTypes = packagingTypes;
		});
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes['piece']) {
			if (this.piece) {
				this.patchDgPieceForm(this.piece);
			} else {
				this.sliDgPieceForm.reset();
			}
		}
	}

	private patchDgPieceForm(piece: Piece) {
		this.sliDgPieceForm.patchValue(
			{
				productDescription: piece.productDg?.description,
				typeOfPackage: {
					typeCode: piece.packagingType.typeCode,
					description: piece.packagingType.description,
				},
				packagedIdentifier: piece.packagedIdentifier as any,
				whetherHaveDeclaredValueForCustoms: piece.nvdForCustoms,
				whetherHaveDeclaredValueForCarriage: piece.nvdForCarriage,
				specialProvisionId: piece.productDg?.specialProvisionId as any,
				explosiveCompatibilityGroupCode: piece.productDg?.explosiveCompatibilityGroupCode as any,
				packagingDangerLevelCode: piece.productDg?.packagingDangerLevelCode as any,
				technicalName: piece.productDg?.technicalName as any,
				unNumber: piece.productDg?.unNumber as any,
				shippersDeclaration: piece.dgDeclaration?.shipperDeclarationText as any,
				handlingInformation: piece.dgDeclaration?.handlingInformation as any,
				complianceDeclaration: piece.dgDeclaration?.complianceDeclarationText as any,
				exclusiveUseIndicator: piece.dgDeclaration?.exclusiveUseIndicator as any,
				aircraftLimitationInformation: piece.dgDeclaration?.aircraftLimitationInformation as any,
				allPackedInOne: piece.allPackedInOneIndicator as boolean,
				qValueNumeric: piece.qvalueNumeric as number,
				upid: piece.upid as any,
				shippingMarks: piece.shippingMarks as any,
				grossWeight: piece.grossWeight as any,
				dimensions: {
					length: piece.dimensions?.length as any,
					width: piece.dimensions?.width as any,
					height: piece.dimensions?.height as any,
				},
				hsCommodityDescription: piece.productDg?.hsCommodityDescription as any,
				properShippingName: piece.productDg?.properShippingName as any,
				textualHandlingInstructions: piece.textualHandlingInstructions as any,
				hazardClassificationId: piece.productDg?.hazardClassificationId as any,
				additionalHazardClassificationId: piece.productDg?.additionalHazardClassificationId as any,
				packingInstructionNumber: piece.productDg?.packingInstructionNumber as any,
				authorizationInformation: piece.productDg?.authorizationInformation as any,
			},
			{
				emitEvent: false,
			}
		);

		const { containedItems } = piece;
		if (Array.isArray(containedItems)) {
			this.dgPieceItems = containedItems;
		}
	}

	onTypeOfPackageChange(code: string) {
		const typeOfPackage = this.packagingTypes.find((packagingType: CodeName) => packagingType.code === code);

		if (typeOfPackage) {
			this.sliDgPieceForm.get('typeOfPackage')?.patchValue({
				typeCode: typeOfPackage.code,
				description: typeOfPackage.name,
			});
		} else {
			this.sliDgPieceForm.get('typeOfPackage')?.patchValue({
				typeCode: '',
				description: '',
			});
		}
	}

	hasValidTouchedFormData() {
		return this.sliDgPieceForm.touched && this.sliDgPieceForm.valid;
	}
}
