<div>
	<h2 class="mat-display-2 orll-sli-shipper-form__title">{{ 'sli.dgPiece.title' | translate }}</h2>
	<form [formGroup]="sliDgPieceForm">
		<div class="row">
			<div class="col-6">
				<div class="row">
					<mat-form-field appearance="outline" class="col-12" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.productDescription' | translate }}</mat-label>
						<textarea class="dg-piece-form-textarea" matInput
							formControlName="productDescription"></textarea>
						@if (sliDgPieceForm.get('productDescription')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.productDescription' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.typeOfPackage' | translate }}</mat-label>
						<input type="text" matInput [(ngModel)]="typeOfPackage"
							(ngModelChange)="onTypeOfPackageChange($event)"
							[ngModelOptions]="{standalone: true, updateOn: 'change'}"
							[matAutocomplete]="autoPackagingType" required>
						<mat-autocomplete #autoPackagingType>
							@for (packagingTypes of filteredPackagingTypes; track packagingTypes) {
								<mat-option [value]="packagingTypes.code">{{ packagingTypes.code }}
									- {{ packagingTypes.name }}
								</mat-option>
							}
						</mat-autocomplete>

						@if (sliDgPieceForm.get('typeOfPackage')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.typeOfPackage' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-8" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.packagedIdentifier' | translate }}</mat-label>
						<input matInput formControlName="packagedIdentifier">
						@if (sliDgPieceForm.get('packagedIdentifier')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.packagedIdentifier' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-6" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.whetherHaveDeclaredValueForCustoms' | translate }}</mat-label>
						<mat-select formControlName="whetherHaveDeclaredValueForCustoms">
							<mat-option [value]="true">NCV</mat-option>
							<mat-option [value]="false">Yes</mat-option>
						</mat-select>
						@if (sliDgPieceForm.get('whetherHaveDeclaredValueForCustoms')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.whetherHaveDeclaredValueForCustoms' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-6" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.whetherHaveDeclaredValueForCarriage' | translate }}</mat-label>
						<mat-select formControlName="whetherHaveDeclaredValueForCarriage">
							<mat-option [value]="true">NVD</mat-option>
							<mat-option [value]="false">Yes</mat-option>
						</mat-select>
						@if (sliDgPieceForm.get('whetherHaveDeclaredValueForCarriage')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.whetherHaveDeclaredValueForCarriage' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.specialProvisionId' | translate }}</mat-label>
						<input matInput formControlName="specialProvisionId">
						@if (sliDgPieceForm.get('specialProvisionId')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.specialProvisionId' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.explosiveCompatibilityGroupCode' | translate }}</mat-label>
						<mat-select formControlName="explosiveCompatibilityGroupCode">
							<mat-option value=""></mat-option>
							<mat-option value="A">A</mat-option>
							<mat-option value="B">B</mat-option>
							<mat-option value="C">C</mat-option>
							<mat-option value="D">D</mat-option>
							<mat-option value="E">E</mat-option>
							<mat-option value="F">F</mat-option>
							<mat-option value="G">G</mat-option>
							<mat-option value="H">H</mat-option>
							<mat-option value="J">J</mat-option>
							<mat-option value="K">K</mat-option>
							<mat-option value="L">L</mat-option>
							<mat-option value="N">N</mat-option>
							<mat-option value="S">S</mat-option>
						</mat-select>
						@if (sliDgPieceForm.get('explosiveCompatibilityGroupCode')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.explosiveCompatibilityGroupCode' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.packagingDangerLevelCode' | translate }}</mat-label>
						<mat-select formControlName="packagingDangerLevelCode">
							<mat-option value=""></mat-option>
							<mat-option value="I">I</mat-option>
							<mat-option value="II">II</mat-option>
							<mat-option value="III">III</mat-option>
						</mat-select>
						@if (sliDgPieceForm.get('packagingDangerLevelCode')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.packagingDangerLevelCode' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.technicalName' | translate }}</mat-label>
						<input matInput formControlName="technicalName">
						@if (sliDgPieceForm.get('technicalName')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.technicalName' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.unNumber' | translate }}</mat-label>
						<input matInput formControlName="unNumber">
						@if (sliDgPieceForm.get('unNumber')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.unNumber' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.shippersDeclaration' | translate }}</mat-label>
						<input matInput formControlName="shippersDeclaration">
						@if (sliDgPieceForm.get('shippersDeclaration')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.shippersDeclaration' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-12" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.handlingInformation' | translate }}</mat-label>
						<textarea class="dg-piece-form-textarea" matInput formControlName="handlingInformation">
						</textarea>
						@if (sliDgPieceForm.get('handlingInformation')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.handlingInformation' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
			</div>
			<div class="col-6">
				<div class="row">
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.allPackedInOne' | translate }}</mat-label>
						<mat-select formControlName="allPackedInOne">
							<mat-option [value]="true">Y</mat-option>
							<mat-option [value]="false">N</mat-option>
						</mat-select>
						@if (sliDgPieceForm.get('allPackedInOne')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.allPackedInOne' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.qValueNumeric' | translate }}</mat-label>
						<input matInput type="number" formControlName="qValueNumeric">
						@if (sliDgPieceForm.get('qValueNumeric')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.qValueNumeric' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.upid' | translate }}</mat-label>
						<input matInput formControlName="upid">
						@if (sliDgPieceForm.get('upid')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.upid' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.shippingMarks' | translate }}</mat-label>
						<input matInput formControlName="shippingMarks">
						@if (sliDgPieceForm.get('shippingMarks')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.shippingMarks' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.grossWeight' | translate }}</mat-label>
						<input matInput formControlName="grossWeight" required>
						<span matSuffix class="unit">KG</span>
						@if (sliDgPieceForm.get('grossWeight')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.grossWeight' | translate } }}</mat-error>
						}
						@if (sliDgPieceForm.get('grossWeight')?.hasError('pattern')) {
							<mat-error>{{
									'validators.maxOneDecimal'|translate:{
										field: 'sli.dgPiece.formItem.grossWeight' | translate,
									}
								}}
							</mat-error>
						}
					</mat-form-field>
					<div class="col-8">
						<div class="d-flex" formGroupName="dimensions">
							<mat-form-field appearance="outline" class="negative-mr-1" floatLabel="always">
								<mat-label>{{ 'sli.dgPiece.formItem.dimensions' | translate }}</mat-label>
								<input matInput formControlName="length" required>
								<span matTextSuffix>CM</span>
								@if (sliDgPieceForm.get(['dimensions', 'length'])?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.dimensions' | translate } }}</mat-error>
								}
								@if (sliDgPieceForm.get(['dimensions', 'length'])?.hasError('pattern')) {
									<mat-error>{{
											'validators.maxOneDecimal'|translate:{
												field: 'sli.dgPiece.formItem.dimensions' | translate,
											}
										}}
									</mat-error>
								}
							</mat-form-field>
							<mat-form-field appearance="outline" class="negative-mr-1" floatLabel="always">
								<input matInput formControlName="width" required>
								<span matTextSuffix>CM</span>
								@if (sliDgPieceForm.get(['dimensions', 'width'])?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.dimensions' | translate } }}</mat-error>
								}
								@if (sliDgPieceForm.get(['dimensions', 'width'])?.hasError('pattern')) {
									<mat-error>{{
											'validators.maxOneDecimal'|translate:{
												field: 'sli.dgPiece.formItem.dimensions' | translate,
											}
										}}
									</mat-error>
								}
							</mat-form-field>
							<mat-form-field appearance="outline" class="negative-mr-1" floatLabel="always">
								<input matInput formControlName="height" required>
								<span matTextSuffix>CM</span>
								@if (sliDgPieceForm.get(['dimensions', 'height'])?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.dimensions' | translate } }}</mat-error>
								}
								@if (sliDgPieceForm.get(['dimensions', 'height'])?.hasError('pattern')) {
									<mat-error>{{
											'validators.maxOneDecimal'|translate:{
												field: 'sli.dgPiece.formItem.dimensions' | translate,
											}
										}}
									</mat-error>
								}
							</mat-form-field>
						</div>
					</div>

				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-8" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.hsCommodityDescription' | translate }}</mat-label>
						<input matInput formControlName="hsCommodityDescription">
						@if (sliDgPieceForm.get('hsCommodityDescription')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.hsCommodityDescription' | translate } }}</mat-error>
						}
					</mat-form-field>

					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.properShippingName' | translate }}</mat-label>
						<input matInput formControlName="properShippingName">
						@if (sliDgPieceForm.get('properShippingName')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.properShippingName' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<mat-form-field appearance="outline" class="col-8" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.textualHandlingInstructions' | translate }}</mat-label>
						<input matInput formControlName="textualHandlingInstructions">
						@if (sliDgPieceForm.get('textualHandlingInstructions')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.textualHandlingInstructions' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-4" floatLabel="always">
						<mat-label>{{ 'sli.dgPiece.formItem.hazardClassificationId' | translate }}</mat-label>
						<input matInput formControlName="hazardClassificationId">
						@if (sliDgPieceForm.get('hazardClassificationId')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.hazardClassificationId' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>
				<div class="row">
					<div class="col-6">
						<div class="row">
							<mat-form-field appearance="outline" class="col-12" floatLabel="always">
								<mat-label>{{ 'sli.dgPiece.formItem.additionalHazardClassificationId' | translate }}</mat-label>
								<input matInput formControlName="additionalHazardClassificationId">
								@if (sliDgPieceForm.get('additionalHazardClassificationId')?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.additionalHazardClassificationId' | translate } }}</mat-error>
								}
							</mat-form-field>
							<mat-form-field appearance="outline" class="col-12" floatLabel="always">
								<mat-label>{{ 'sli.dgPiece.formItem.packingInstructionNumber' | translate }}</mat-label>
								<input matInput formControlName="packingInstructionNumber">
								@if (sliDgPieceForm.get('packingInstructionNumber')?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.packingInstructionNumber' | translate } }}</mat-error>
								}
							</mat-form-field>
						</div>
					</div>
					<div class="col-6">
						<div class="row">
							<mat-form-field appearance="outline" class="col-12" floatLabel="always">
								<mat-label>{{ 'sli.dgPiece.formItem.authorizationInformation' | translate }}</mat-label>
								<textarea class="dg-piece-form-textarea" matInput
									formControlName="authorizationInformation">
								</textarea>
								@if (sliDgPieceForm.get('authorizationInformation')?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.authorizationInformation' | translate } }}</mat-error>
								}
							</mat-form-field>
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-6">
						<div class="row">
							<mat-form-field appearance="outline" class="col-12" floatLabel="always">
								<mat-label>{{ 'sli.dgPiece.formItem.complianceDeclaration' | translate }}</mat-label>
								<input matInput formControlName="complianceDeclaration">
								@if (sliDgPieceForm.get('complianceDeclaration')?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.complianceDeclaration' | translate } }}</mat-error>
								}
							</mat-form-field>
							<mat-form-field appearance="outline" class="col-12" floatLabel="always">
								<mat-label>{{ 'sli.dgPiece.formItem.exclusiveUseIndicator' | translate }}</mat-label>
								<input matInput formControlName="exclusiveUseIndicator">
								@if (sliDgPieceForm.get('exclusiveUseIndicator')?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.exclusiveUseIndicator' | translate } }}</mat-error>
								}
							</mat-form-field>
						</div>
					</div>
					<div class="col-6">
						<div class="row">
							<mat-form-field appearance="outline" class="col-12" floatLabel="always">
								<mat-label>{{ 'sli.dgPiece.formItem.aircraftLimitationInformation' | translate }}</mat-label>
								<textarea class="dg-piece-form-textarea" matInput
									formControlName="aircraftLimitationInformation"></textarea>
								@if (sliDgPieceForm.get('aircraftLimitationInformation')?.hasError('required')) {
									<mat-error>{{ 'validators.required'|translate:{ field: 'sli.dgPiece.formItem.aircraftLimitationInformation' | translate } }}</mat-error>
								}
							</mat-form-field>
						</div>
					</div>
				</div>

			</div>
		</div>
		<div>
			<orll-piece-item-panel [pieceItemList]="dgPieceItems"></orll-piece-item-panel>
		</div>

	</form>
</div>
