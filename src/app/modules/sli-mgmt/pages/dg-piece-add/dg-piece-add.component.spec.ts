import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DgPieceAddComponent } from './dg-piece-add.component';
import { HTTP_INTERCEPTORS, HttpClient, provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { HttpTestingController, provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { BusinessErrorInterceptor } from '@shared/interceptors/business-error.interceptor';

describe('DgPieceAddComponent', () => {
	let component: DgPieceAddComponent;
	let fixture: ComponentFixture<DgPieceAddComponent>;
	let httpClient: HttpClient;
	let httpTestingController: HttpTestingController;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [DgPieceAddComponent],
			providers: [
				{
					provide: HTTP_INTERCEPTORS,
					useClass: BusinessErrorInterceptor,
					multi: true,
				},
				provideHttpClient(withInterceptorsFromDi()),
				provideHttpClientTesting(),
				provideTranslateService(),
			],
		}).compileComponents();

		httpClient = TestBed.inject(HttpClient);
		httpTestingController = TestBed.inject(HttpTestingController);

		fixture = TestBed.createComponent(DgPieceAddComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(httpClient).toBeInstanceOf(HttpClient);

		const req = httpTestingController.expectOne((req) => req.url.includes('/sys-management/enums/packageType'));
		req.flush({
			code: 200,
			data: [
				{
					code: '1',
					name: 'Package Type 1',
				},
			],
		});

		expect(component).toBeTruthy();

		expect(component.packagingTypes).toEqual([
			{
				code: '1',
				name: 'Package Type 1',
			},
		]);
	});

	it('should initialize form with required controls', () => {
		const form = component.sliDgPieceForm;
		expect(form.get('productDescription')).toBeDefined();
		expect(form.get('typeOfPackage')).toBeDefined();
		expect(form.get('dimensions')).toBeDefined();
		expect(form.get('qValueNumeric')).toBeDefined();
	});

	it('should require qValueNumeric when allPackedInOne is true', () => {
		const qValueCtrl = component.sliDgPieceForm.get('qValueNumeric');

		// Set condition to true
		component.sliDgPieceForm.get('allPackedInOne')?.setValue(true);

		// @ts-expect-error - qValueNumeric is required
		qValueCtrl?.setValue(null);
		expect(qValueCtrl?.invalid).toBeTrue();

		qValueCtrl?.setValue(10);
		expect(qValueCtrl?.valid).toBeTrue();
	});

	it('should reset typeOfPackage when invalid code is provided', () => {
		// Set initial values
		component.sliDgPieceForm.get('typeOfPackage')?.patchValue({
			typeCode: 'TEST',
			description: 'Test Value',
		});

		component.onTypeOfPackageChange('INVALID_CODE');

		const typeGroup = component.sliDgPieceForm.get('typeOfPackage');
		expect(typeGroup?.value).toEqual({
			typeCode: '',
			description: '',
		});
	});

	it('should be valid when mandatory fields are filled', () => {
		// Set required fields
		component.sliDgPieceForm.patchValue({
			productDescription: 'Test Product',
			dimensions: {
				length: '10',
				width: '20',
				height: '30',
			},
		});

		// Handle conditional requirement
		expect(component.sliDgPieceForm.get('dimensions')?.value).toEqual({
			length: '10',
			width: '20',
			height: '30',
		});
	});
});
