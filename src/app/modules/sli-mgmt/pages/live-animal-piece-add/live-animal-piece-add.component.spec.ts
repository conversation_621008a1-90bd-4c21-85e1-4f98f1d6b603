import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LiveAnimalPieceAddComponent } from './live-animal-piece-add.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { By } from '@angular/platform-browser';

describe('LiveAnimalPieceAddComponent', () => {
	let component: LiveAnimalPieceAddComponent;
	let fixture: ComponentFixture<LiveAnimalPieceAddComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [LiveAnimalPieceAddComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(LiveAnimalPieceAddComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should not update typeOfPackage form group when onTypeOfPackageChange is called with invalid code', () => {
		// Set initial form values
		component.sliLiveAnimalPieceForm.get('typeOfPackage')?.patchValue({
			typeCode: 'INITIAL',
			description: 'Initial Value',
		});

		// Call method with invalid code
		component.onTypeOfPackageChange('INVALID_CODE');

		// Values should remain unchanged
		const typeOfPackageGroup = component.sliLiveAnimalPieceForm.get('typeOfPackage');
		expect(typeOfPackageGroup?.get('typeCode')?.value).toBe('INITIAL');
		expect(typeOfPackageGroup?.get('description')?.value).toBe('Initial Value');
	});

	it('should not require optional fields', async () => {
		const optionalFields = ['packagedIdentifier', 'shippingMarks', 'upid', 'textualHandlingInstructions'];

		for (const field of optionalFields) {
			const control = component.sliLiveAnimalPieceForm.get(field);
			control?.setValue('');
			control?.markAsTouched();
			fixture.detectChanges();

			const errorElement = fixture.debugElement.query(By.css(`[formControlName="${field}"] + mat-error`));
			expect(errorElement).toBeNull();
		}
	});
});
