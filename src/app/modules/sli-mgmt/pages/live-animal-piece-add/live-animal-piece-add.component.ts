import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslatePipe } from '@ngx-translate/core';
import { FormsModule, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { MatAutocomplete, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { CodeName } from '@shared/models/code-name.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { Piece } from '../../models/piece/piece.model';

const REGEX_ONE_NUMBER = /^\d+(\.\d{1})?$/;
const REGEX_TWO_NUMBER = /^\d+(\.\d{1,2})?$/;

@Component({
	selector: 'orll-live-animal-piece-add',
	imports: [
		CommonModule,
		ReactiveFormsModule,
		TranslatePipe,
		MatFormFieldModule,
		MatIconModule,
		MatInputModule,
		MatExpansionModule,
		MatButtonModule,
		MatSelectModule,
		MatAutocompleteTrigger,
		FormsModule,
		MatAutocomplete,
	],
	templateUrl: './live-animal-piece-add.component.html',
	styleUrl: './live-animal-piece-add.component.scss',
})
export class LiveAnimalPieceAddComponent implements OnInit, OnChanges {
	@Input() piece: Piece | null = null;

	sliLiveAnimalPieceForm = this.fb.group({
		productDescription: [''],
		typeOfPackage: this.fb.group({
			typeCode: [''],
			description: [''],
		}),
		packagedIdentifier: [''],
		speciesCommonName: [''],
		speciesScientificName: [''],
		specimenDescription: [''],
		animalQuantity: [null, [Validators.required, Validators.pattern(/^\d+$/)]],
		shippingMarks: [''],
		upid: [''],
		grossWeight: ['', [Validators.pattern(REGEX_TWO_NUMBER)]],
		dimensions: this.fb.group({
			length: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
			width: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
			height: this.fb.control<string | null>(null, [Validators.required, Validators.pattern(REGEX_ONE_NUMBER)]),
		}),
		whetherHaveDeclaredValueForCustoms: [true],
		whetherHaveDeclaredValueForCarriage: [true],
		textualHandlingInstructions: [''],
	});

	typeOfPackage = '';

	packagingTypes: CodeName[] = [];
	filteredPackagingTypes: CodeName[] = [];

	constructor(
		private readonly fb: NonNullableFormBuilder,
		public readonly sliCreateRequestService: SliCreateRequestService
	) {}

	ngOnInit() {
		this.sliCreateRequestService.getPackingTypes().subscribe((packagingTypes: CodeName[]) => {
			this.packagingTypes = packagingTypes;
			this.filteredPackagingTypes = [...packagingTypes];
		});
	}

	ngOnChanges(changes: SimpleChanges) {
		if (changes['piece']) {
			if (this.piece) {
				this.patchLiveAnimalPieceForm(this.piece);
			} else {
				this.sliLiveAnimalPieceForm.reset();
			}
		}
	}

	private patchLiveAnimalPieceForm(piece: Piece) {
		this.sliLiveAnimalPieceForm.patchValue({
			productDescription: piece.product?.description,
			typeOfPackage: {
				typeCode: piece.packagingType?.typeCode,
				description: piece.packagingType?.description,
			},
			packagedIdentifier: piece.packagedIdentifier,
			speciesCommonName: piece.speciesCommonName,
			speciesScientificName: piece.speciesScientificName,
			specimenDescription: piece.specimenDescription,
			animalQuantity: piece.quantityAnimals as any,
			shippingMarks: piece.shippingMarks as any,
			upid: piece.upid as any,
			grossWeight: piece.grossWeight as any,
			dimensions: {
				length: piece.dimensions?.length as any,
				width: piece.dimensions?.width as any,
				height: piece.dimensions?.height as any,
			},
			whetherHaveDeclaredValueForCustoms: piece.nvdForCustoms as any,
			whetherHaveDeclaredValueForCarriage: piece.nvdForCarriage as any,
			textualHandlingInstructions: piece.textualHandlingInstructions,
		});
	}

	onTypeOfPackageChange(code: string) {
		const typeOfPackage = this.packagingTypes.find((packagingType: CodeName) => packagingType.code === code);

		if (typeOfPackage) {
			this.sliLiveAnimalPieceForm.get('typeOfPackage')?.patchValue({
				typeCode: typeOfPackage.code,
				description: typeOfPackage.name,
			});
		}
	}

	hasValidTouchedFormData() {
		return this.sliLiveAnimalPieceForm.touched && this.sliLiveAnimalPieceForm.valid;
	}
}
