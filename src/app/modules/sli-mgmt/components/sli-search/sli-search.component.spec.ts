import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliSearchComponent } from './sli-search.component';
import { ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { DatePipe } from '@angular/common';
import { SliSearchRequestService } from '../../services/sli-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { TranslateModule } from '@ngx-translate/core';
import { noop } from 'rxjs';

describe('SliSearchComponent', () => {
	let component: SliSearchComponent;
	let fixture: ComponentFixture<SliSearchComponent>;
	let mockSearchService: Partial<SliSearchRequestService>;

	beforeEach(async () => {
		mockSearchService = {
			getOptions: jasmine.createSpy('getOptions'),
		};

		await TestBed.configureTestingModule({
			imports: [ReactiveFormsModule, MatDatepickerModule, MatNativeDateModule, TranslateModule.forRoot()],
			providers: [DatePipe, { provide: SliSearchRequestService, useValue: mockSearchService }],
		}).compileComponents();

		fixture = TestBed.createComponent(SliSearchComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize the sliSearchForm', () => {
		expect(component.sliSearchForm).toBeDefined();
		expect(component.sliSearchForm.controls['goodsDescription']).toBeDefined();
		expect(component.sliSearchForm.controls['startDate']).toBeDefined();
		expect(component.sliSearchForm.controls['endDate']).toBeDefined();
	});

	it('onSearch() the searchSli emit should be allowed when search button is clicked', () => {
		spyOn(component.searchSli, 'emit');

		component.onSearch();

		expect(component.searchSli.emit).toHaveBeenCalled();
	});

	it('onReset() should reset all the form fields and selected items', () => {
		component.sliSearchForm.patchValue({ goodsDescription: 'test', startDate: new Date(), endDate: new Date() });
		component.selectedShippers = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedConsignees = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedSliCodes = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedHawbNumbers = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedDepartureLocations = [{ code: '1', name: 'Test' }] as CodeName[];
		component.selectedArrivalLocations = [{ code: '1', name: 'Test' }] as CodeName[];

		component.onReset({ preventDefault: noop, stopPropagation: noop } as any);

		expect(component.sliSearchForm.value).toEqual({
			goodsDescription: null,
			startDate: null,
			endDate: null,
		});
		expect(component.selectedShippers).toEqual([]);
		expect(component.selectedConsignees).toEqual([]);
		expect(component.selectedSliCodes).toEqual([]);
		expect(component.selectedHawbNumbers).toEqual([]);
		expect(component.selectedDepartureLocations).toEqual([]);
		expect(component.selectedArrivalLocations).toEqual([]);
	});

	it('selectedItems() should update the selected items', () => {
		const testItems = [{ code: '1', name: 'Test' }] as CodeName[];

		component.selectedItems(testItems, 'shipper');
		expect(component.selectedShippers).toEqual(testItems);

		component.selectedItems(testItems, 'consignee');
		expect(component.selectedConsignees).toEqual(testItems);

		component.selectedItems(testItems, 'sliCode');
		expect(component.selectedSliCodes).toEqual(testItems);

		component.selectedItems(testItems, 'hawbNumber');
		expect(component.selectedHawbNumbers).toEqual(testItems);

		component.selectedItems(testItems, 'departureLocation');
		expect(component.selectedDepartureLocations).toEqual(testItems);

		component.selectedItems(testItems, 'arrivalLocation');
		expect(component.selectedArrivalLocations).toEqual(testItems);
	});
});
