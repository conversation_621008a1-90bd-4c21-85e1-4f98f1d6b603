<div class="orll-sli-shipper-form">
	<form [formGroup]="sliShipperForm">
		<h2 class="mat-display-2 orll-sli-shipper-form__title">{{'sli.mgmt.company.shipper' | translate}}</h2>
		<div class="row">
			<mat-form-field appearance="outline" class="col-6" floatLabel="always">
				<mat-label>{{'sli.mgmt.company.companyName' | translate}}</mat-label>
				<input matInput formControlName="companyName" required>
				@if (sliShipperForm.get('companyName')?.hasError('required')) {
					<mat-error>{{'sli.mgmt.company.companyName.required' | translate}}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-6" floatLabel="always">
				<mat-label>{{'sli.mgmt.company.contactName' | translate}}</mat-label>
				<input matInput formControlName="contactName" required>
				@if (sliShipperForm.get('contactName')?.hasError('required')) {
					<mat-error>{{'sli.mgmt.company.contactName.required' | translate}}</mat-error>
				}
			</mat-form-field>
		</div>

		<div class="row">
			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				<mat-label>{{'sli.mgmt.company.country' | translate}}</mat-label>
				<input type="text" matInput
					formControlName="countryCode"
					[matAutocomplete]="autoCountry">
				<mat-autocomplete #autoCountry="matAutocomplete" [displayWith]="displayCountryName"
					(optionSelected)="countryValueChange($event)">
					@for (country of filteredCountries; track country) {
						<mat-option [value]="country.code">{{country.name}}</mat-option>
					}
				</mat-autocomplete>
				@if (sliShipperForm.get('countryCode')?.hasError('required')) {
					<mat-error>{{'sli.mgmt.company.country.required' | translate}}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				<mat-label>{{'sli.mgmt.company.province' | translate}}</mat-label>
				<input type="text" matInput
					formControlName="regionCode"
					[matAutocomplete]="autoRegion">
				<mat-autocomplete #autoRegion="matAutocomplete" [displayWith]="displayProvinceName"
					(optionSelected)="regionValueChange($event)">
					@for (province of filteredProvinces; track province) {
						<mat-option [value]="province.code">{{province.name}}</mat-option>
					}
				</mat-autocomplete>
				@if (sliShipperForm.get('regionCode')?.hasError('required')) {
					<mat-error>{{'sli.mgmt.company.province.required' | translate}}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-icon matSuffix class="autocomplete-arrow">keyboard_arrow_down</mat-icon>
				<mat-label>{{'sli.mgmt.company.city' | translate}}</mat-label>
				<input type="text" matInput
					formControlName="cityCode"
					[matAutocomplete]="autoCity">
				<mat-autocomplete #autoCity="matAutocomplete" [displayWith]="displayCityName">
					@for (city of filteredCities; track city) {
						<mat-option [value]="city.code">{{city.name}}</mat-option>
					}
				</mat-autocomplete>
				@if (sliShipperForm.get('cityCode')?.hasError('required')) {
					<mat-error>{{'sli.mgmt.company.city.required' | translate}}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{'sli.mgmt.company.textualPostCode' | translate}}</mat-label>
				<input matInput formControlName="textualPostCode">
			</mat-form-field>
		</div>

		<div class="row">
			<mat-form-field appearance="outline" class="col-6" floatLabel="always">
				<mat-label>{{'sli.mgmt.company.address' | translate}}</mat-label>
				<input matInput formControlName="locationName" required>
				@if (sliShipperForm.get('locationName')?.hasError('required')) {
					<mat-error>{{'sli.mgmt.company.address.required' | translate}}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{'sli.mgmt.company.phoneNumber' | translate}}</mat-label>
				<input matInput formControlName="phoneNumber" required>
				@if (sliShipperForm.get('phoneNumber')?.hasError('required')) {
					<mat-error>{{'sli.mgmt.company.phoneNumber.required' | translate}}</mat-error>
				}
			</mat-form-field>

			<mat-form-field appearance="outline" class="col-3" floatLabel="always">
				<mat-label>{{'sli.mgmt.company.emailAddress' | translate}}</mat-label>
				<input matInput formControlName="emailAddress">
				@if (sliShipperForm.get('emailAddress')?.hasError('email')) {
					<mat-error>{{'sli.mgmt.company.pattern.email' | translate}}</mat-error>
				}
			</mat-form-field>
		</div>
	</form>
</div>
