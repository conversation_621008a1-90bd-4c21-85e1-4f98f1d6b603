import { Component, ChangeDetectionStrategy, OnInit, Input, SimpleChanges, OnChanges } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { CommonModule } from '@angular/common';
import { Country } from '../../models/country.model';
import { Province } from '../../models/province.model';
import { ShipmentParty } from '../../models/shipment-party.model';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { startWith } from 'rxjs';
import { MatIconModule } from '@angular/material/icon';
import { OrgInfo } from '@shared/models/org-info.model';
import { OrgType } from '@shared/models/org-type.model';

@Component({
	selector: 'orll-sli-shipper',
	templateUrl: './sli-shipper.component.html',
	styleUrls: ['./sli-shipper.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatInputModule,
		MatSelectModule,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		CommonModule,
		MatAutocompleteModule,
	],
})
export class SliShipperComponent extends DestroyRefComponent implements OnInit, OnChanges {
	countries: Country[] = [];
	provinces: Province[] = [];
	cities: CodeName[] = [];
	filteredCountries: Country[] = [];
	filteredProvinces: Province[] = [];
	filteredCities: CodeName[] = [];

	@Input() shipperInfo: OrgInfo | ShipmentParty | null = null;

	sliShipperForm: FormGroup = new FormGroup({
		companyName: new FormControl<string>('', [Validators.required]),
		contactName: new FormControl<string>('', [Validators.required]),
		countryCode: new FormControl<string>('', [Validators.required]),
		regionCode: new FormControl<string>('', [Validators.required]),
		cityCode: new FormControl<string>('', [Validators.required]),
		textualPostCode: new FormControl<string>(''),
		locationName: new FormControl<string>('', [Validators.required]),
		phoneNumber: new FormControl<string>('', [Validators.required]),
		emailAddress: new FormControl<string>('', [Validators.email]),
	});

	constructor(private readonly sliCreateRequestService: SliCreateRequestService) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();
	}

	ngOnChanges(changes: SimpleChanges): void {
		if (changes['shipperInfo']) {
			this.fillShipperInfo();
		}
	}

	private initRefData(): void {
		this.sliCreateRequestService.getCountries().subscribe((countries: Country[]) => {
			this.countries = countries;
			this.filteredCountries = countries;
		});
	}

	private isOrgInfo(info: OrgInfo | ShipmentParty): info is OrgInfo {
		return 'persons' in info;
	}

	private getShipperInfo(info: OrgInfo | ShipmentParty): ShipmentParty {
		if (this.isOrgInfo(info)) {
			const contact = info.persons.find((person) => person.contactRole === OrgType.CUSTOMER_CONTACT);
			return {
				companyName: info.companyName,
				contactName: contact?.contactName ?? '',
				countryCode: info.countryCode,
				regionCode: info.regionCode,
				cityCode: info.cityCode,
				textualPostCode: info.textualPostCode,
				locationName: info.locationName,
				phoneNumber: contact?.phoneNumber ?? '',
				emailAddress: contact?.emailAddress ?? '',
				companyType: info.partyRole,
			};
		} else {
			return {
				companyName: info.companyName,
				contactName: info.contactName,
				countryCode: info.countryCode,
				regionCode: info.regionCode,
				cityCode: info.cityCode,
				textualPostCode: info.textualPostCode,
				locationName: info.locationName,
				phoneNumber: info.phoneNumber,
				emailAddress: info.emailAddress,
				companyType: info.companyType,
			};
		}
	}

	fillShipperInfo(): void {
		if (!this.shipperInfo) return;

		this.setupCountryValueChange(this.shipperInfo.countryCode);
		this.setupRegionValueChange(this.shipperInfo.regionCode);

		const shipperInfo = this.getShipperInfo(this.shipperInfo);
		this.sliShipperForm.patchValue(shipperInfo);
	}

	private setupAutocomplete(): void {
		this.sliShipperForm
			.get('countryCode')
			?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
			.subscribe((search) => {
				this.filteredCountries = this.countries.filter((country) =>
					country.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
				);
			});
	}

	displayCountryName = (code: string): string => {
		const country = this.countries.find((item) => item.code === code);
		return country?.name ?? '';
	};

	displayProvinceName = (code: string): string => {
		const province = this.provinces.find((item) => item.code === code);
		return province?.name ?? '';
	};

	displayCityName = (code: string): string => {
		const city = this.cities.find((item) => item.code === code);
		return city?.name ?? '';
	};

	private setupCountryValueChange(value: string): void {
		const selectedCountry: Country[] = this.countries.filter((country: Country) => country.code === value);
		this.sliCreateRequestService.getProvinces(selectedCountry[0]).subscribe((provinces: Province[]) => {
			this.provinces = provinces;
			this.filteredProvinces = provinces;

			this.sliShipperForm
				.get('regionCode')
				?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
				.subscribe((search) => {
					this.filteredProvinces = this.provinces.filter((province) =>
						province.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
					);
				});
		});
	}

	private setupRegionValueChange(value: string): void {
		const selectedProvince: Province[] = this.provinces.filter((province: Province) => province.code === value);
		this.sliCreateRequestService.getCities(selectedProvince[0]).subscribe((cities: CodeName[]) => {
			this.cities = cities;
			this.filteredCities = cities;

			this.sliShipperForm
				.get('cityCode')
				?.valueChanges.pipe(startWith(''), takeUntilDestroyed(this.destroyRef))
				.subscribe((search) => {
					this.filteredCities = this.cities.filter((city) =>
						city.name.toLowerCase().includes(search?.toLowerCase().trim() ?? '')
					);
				});
		});
	}

	countryValueChange(event?: MatAutocompleteSelectedEvent): void {
		this.sliShipperForm.get('regionCode')?.setValue('');
		this.sliShipperForm.get('cityCode')?.setValue('');

		const value = event?.option.value ?? '';
		this.setupCountryValueChange(value);
	}

	regionValueChange(event?: MatAutocompleteSelectedEvent): void {
		const value = event?.option.value ?? '';
		this.setupRegionValueChange(value);
	}

	getFormData(ignore?: boolean): ShipmentParty | null {
		if (!ignore && this.sliShipperForm.invalid) {
			return null;
		}
		return {
			companyName: this.sliShipperForm.value.companyName!,
			contactName: this.sliShipperForm.value.contactName!,
			countryCode: this.sliShipperForm.value.countryCode!,
			regionCode: this.sliShipperForm.value.regionCode!,
			cityCode: this.sliShipperForm.value.cityCode!,
			textualPostCode: this.sliShipperForm.value.textualPostCode,
			locationName: this.sliShipperForm.value.locationName!,
			phoneNumber: this.sliShipperForm.value.phoneNumber!,
			emailAddress: this.sliShipperForm.value.emailAddress,
			companyType: '',
		};
	}
}
