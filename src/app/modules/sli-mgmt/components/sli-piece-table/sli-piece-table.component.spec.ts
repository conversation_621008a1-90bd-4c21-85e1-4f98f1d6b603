import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliPieceTableComponent } from './sli-piece-table.component';
import { MatTableDataSource } from '@angular/material/table';
import { SelectionModel } from '@angular/cdk/collections';
import { TranslateModule } from '@ngx-translate/core';
import { PageEvent } from '@angular/material/paginator';
import { SimpleChange } from '@angular/core';
import { PieceList } from '../../models/piece/piece-list.model';
import { MatDialog } from '@angular/material/dialog';
import { of } from 'rxjs';
import { Sort } from '@angular/material/sort';
import { PieceType } from '../../models/piece/piece-type.model';

describe('SliPieceTableComponent', () => {
	let component: SliPieceTableComponent;
	let fixture: ComponentFixture<SliPieceTableComponent>;
	let mockPieces: PieceList[];
	let selectedItems: PieceList[];
	let mockDialog: jasmine.SpyObj<MatDialog>;

	beforeEach(async () => {
		selectedItems = [];
		const mockSelection = {
			clear: jasmine.createSpy('clear').and.callFake(() => {
				selectedItems.length = 0;
				return mockSelection;
			}),
			select: jasmine.createSpy('select').and.callFake((...pieces: PieceList[]) => {
				pieces.forEach((piece) => {
					if (!selectedItems.includes(piece)) {
						selectedItems.push(piece);
					}
				});
				return mockSelection;
			}),
			get selected() {
				return selectedItems;
			},
		} as any as SelectionModel<PieceList>;

		mockPieces = [
			{
				type: 'Piece',
				pieceId: 'TEST123',
				productDescription: '123',
				packagingType: '456',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			} as PieceList,
			{
				type: 'Piece',
				pieceId: 'TEST456',
				productDescription: '456',
				packagingType: '789',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			} as PieceList,
		];

		mockDialog = jasmine.createSpyObj('MatDialog', ['open']);

		await TestBed.configureTestingModule({
			imports: [SliPieceTableComponent, TranslateModule.forRoot()],
			providers: [{ provide: MatDialog, useValue: mockDialog }],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceTableComponent);
		component = fixture.componentInstance;

		// Mock dependencies
		component.selection = mockSelection;
		component.dataSource = new MatTableDataSource<PieceList>(mockPieces);
		component.pageParams = { pageNum: 1, pageSize: 10 };
		component.totalRecords = 20;
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	describe('ngOnChanges', () => {
		it('should update dataSource and clear selection when records change', () => {
			// Set up the component with initial empty data
			component.dataSource = new MatTableDataSource<PieceList>([]);

			// Create a changes object with the records property
			const changes = {
				records: new SimpleChange(null, mockPieces, true),
			};

			// Set the records input property
			component.records = mockPieces;

			// Call ngOnChanges with the changes object
			component.ngOnChanges(changes);

			// Verify that dataSource.data contains the mock pieces
			expect(component.dataSource.data.length).toEqual(2);
			expect(component.dataSource.data[0]).toEqual(mockPieces[0]);
			expect(component.dataSource.data[1]).toEqual(mockPieces[1]);

			// Verify that selection was cleared
			expect(component.selection.clear).toHaveBeenCalled();
		});

		it('should not update dataSource when records do not change', () => {
			// Set up the component with initial data
			const initialData = [...mockPieces];
			component.dataSource = new MatTableDataSource<PieceList>(initialData);

			// Create a changes object without the records property
			const changes = {
				totalRecords: new SimpleChange(0, 20, true),
			};

			// Call ngOnChanges with the changes object
			component.ngOnChanges(changes);

			// Verify that dataSource.data remains unchanged
			expect(component.dataSource.data).toEqual(initialData);
			expect(component.selection.clear).not.toHaveBeenCalled();
		});
	});

	describe('onSortChange', () => {
		it('should update currentSort and emit sortChange event', () => {
			spyOn(component.sortChange, 'emit');
			spyOn(component.pagination, 'emit');

			const sort: Sort = { active: 'productDescription', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.currentSort).toEqual(sort);
			expect(component.sortChange.emit).toHaveBeenCalledWith(sort);
			expect(component.pagination.emit).toHaveBeenCalled();
		});
	});

	describe('emitPaginationWithSort', () => {
		it('should emit pagination event with current sort parameters when no event provided', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'productDescription', direction: 'asc' };

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort();

			expect(component.pagination.emit).toHaveBeenCalledWith({
				pageIndex: 0,
				pageSize: 10,
				length: 20,
				sortField: 'productDescription',
				sortDirection: 'asc',
			});
		});

		it('should emit pagination event with provided event and current sort parameters', () => {
			spyOn(component.pagination, 'emit');
			component.currentSort = { active: 'grossWeight', direction: 'desc' };

			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 50,
				length: 100,
			};

			// Call the private method using type assertion
			(component as any).emitPaginationWithSort(pageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith({
				...pageEvent,
				sortField: 'grossWeight',
				sortDirection: 'desc',
			});
		});
	});

	describe('isAllSelected', () => {
		it('should return false when no rows are selected', () => {
			selectedItems.length = 0;
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when some rows are selected', () => {
			selectedItems.length = 0;
			component.selection.select(mockPieces[0]);
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return true when all rows are selected', () => {
			selectedItems.length = 0;
			component.selection.select(...mockPieces);
			expect(component.isAllSelected()).toBe(true);
		});

		it('should return false when dataSource is empty', () => {
			selectedItems.length = 0;
			component.dataSource = new MatTableDataSource<PieceList>([]);
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when dataSource is empty but selection is not', () => {
			selectedItems.length = 0;
			component.dataSource = new MatTableDataSource<PieceList>([]);
			component.selection.select(mockPieces[0]);
			expect(component.isAllSelected()).toBe(false);
		});

		it('should return false when more items are selected than exist in dataSource', () => {
			selectedItems.length = 0;
			const extraPiece = {
				type: 'Piece',
				pieceId: 'TEST789',
				productDescription: '123',
				packagingType: '456',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			} as PieceList;
			component.selection.select(...mockPieces, extraPiece);
			expect(component.isAllSelected()).toBe(false);
		});
	});

	describe('toggleAllRows', () => {
		it('should clear selection when all rows are selected', () => {
			selectedItems.length = 0;
			component.dataSource.data = mockPieces;
			component.selection.select(...mockPieces);
			const clearSpy = component.selection.clear;

			component.toggleAllRows();

			expect(clearSpy).toHaveBeenCalled();
		});

		it('should select all when none selected', () => {
			selectedItems.length = 0;
			component.dataSource.data = mockPieces;
			const selectSpy = component.selection.select;

			component.toggleAllRows();

			expect(selectSpy).toHaveBeenCalled();
			expect(selectedItems.length).toBe(mockPieces.length);
		});
	});

	describe('trackByPieceId', () => {
		it('should return the pieceId of the given piece', () => {
			const mockPiece: PieceList = {
				type: 'Piece',
				pieceId: 'TEST123',
				productDescription: '123',
				packagingType: '456',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			};

			expect(component.trackByPieceId(mockPiece)).toBe('TEST123');
		});
	});

	describe('addPiece', () => {
		it('should open dialog and emit saveRequest when result is returned', () => {
			const dialogRefMock = {
				afterClosed: () => of('general'),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.saveRequest, 'emit');

			component.addPiece();

			expect(mockDialog.open).toHaveBeenCalled();
			expect(component.saveRequest.emit).toHaveBeenCalledWith({ pieceType: 'general' });
		});

		it('should not emit saveRequest when dialog is closed without result', () => {
			const dialogRefMock = {
				afterClosed: () => of(undefined),
			};
			mockDialog.open.and.returnValue(dialogRefMock as any);
			spyOn(component.saveRequest, 'emit');

			component.addPiece();

			expect(mockDialog.open).toHaveBeenCalled();
			expect(component.saveRequest.emit).not.toHaveBeenCalled();
		});
	});

	describe('editPiece', () => {
		it('should stop event propagation and emit saveRequest with general pieceType', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const piece = { ...mockPieces[0], type: PieceType.GENERAL };
			spyOn(component.saveRequest, 'emit');

			component.editPiece(eventMock, piece);

			expect(eventMock.stopPropagation).toHaveBeenCalled();
			expect(component.saveRequest.emit).toHaveBeenCalledWith({
				pieceType: 'general',
				pieceId: piece.pieceId,
			});
		});

		it('should emit saveRequest with dg pieceType for dangerous goods', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const piece = { ...mockPieces[0], type: PieceType.DANGEROUS_GOODS };
			spyOn(component.saveRequest, 'emit');

			component.editPiece(eventMock, piece);

			expect(component.saveRequest.emit).toHaveBeenCalledWith({
				pieceType: 'dg',
				pieceId: piece.pieceId,
			});
		});

		it('should emit saveRequest with la pieceType for live animals', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const piece = { ...mockPieces[0], type: PieceType.LIVE_ANIMALS };
			spyOn(component.saveRequest, 'emit');

			component.editPiece(eventMock, piece);

			expect(component.saveRequest.emit).toHaveBeenCalledWith({
				pieceType: 'la',
				pieceId: piece.pieceId,
			});
		});

		it('should emit saveRequest with empty pieceType for unknown type', () => {
			const eventMock = jasmine.createSpyObj('Event', ['stopPropagation']);
			const piece = { ...mockPieces[0], type: 'Unknown' as any };
			spyOn(component.saveRequest, 'emit');

			component.editPiece(eventMock, piece);

			expect(component.saveRequest.emit).toHaveBeenCalledWith({
				pieceType: '',
				pieceId: piece.pieceId,
			});
		});
	});

	describe('pagination', () => {
		it('should emit pagination event when page changes', () => {
			spyOn(component.pagination, 'emit');
			const pageEvent: PageEvent = {
				pageIndex: 1,
				pageSize: 10,
				length: 100,
			};

			component.pagination.emit(pageEvent);

			expect(component.pagination.emit).toHaveBeenCalledWith(pageEvent);
		});
	});
});
