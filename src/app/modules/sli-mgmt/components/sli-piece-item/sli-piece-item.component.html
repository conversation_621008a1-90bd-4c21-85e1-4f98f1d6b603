<div class="orll-piece-add-item">
	@if (pieceType === 'general') {
		<div class="orll-piece-add-item__header">
			<div class="orll-piece-add-item__title">{{'sli.piece.item.title' | translate}}</div>
			<mat-radio-group [(ngModel)]="isContained" class="orll-piece-add-item__radio-group"
				aria-label="{{'sli.piece.item.title' | translate}}">
				<mat-radio-button value="yes">{{'sli.piece.contained.yes' | translate}}</mat-radio-button>
				<mat-radio-button value="no">{{'sli.piece.contained.no' | translate}}</mat-radio-button>
			</mat-radio-group>
		</div>
	}

	<div class="orll-piece-add-item__body">
		@if (isContained === 'no') {
			<orll-piece-item-panel [pieceItemList]="piece?.containedItems ?? []"></orll-piece-item-panel>
		} @else {
			<orll-piece-in-panel [pieceInList]="piece?.containedPieces ?? []"></orll-piece-in-panel>
		}
	</div>
</div>
