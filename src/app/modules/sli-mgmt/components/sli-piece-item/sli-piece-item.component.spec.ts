import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliPieceItemComponent } from './sli-piece-item.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { MatRadioModule } from '@angular/material/radio';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SimpleChange } from '@angular/core';
import { Piece } from '../../models/piece/piece.model';
import { PieceItem } from '../../models/piece/piece-item.model';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('SliPieceItemComponent', () => {
	let component: SliPieceItemComponent;
	let fixture: ComponentFixture<SliPieceItemComponent>;

	// Mock data
	const mockPiece: Piece = {
		upid: 'TEST123',
		type: 'Piece',
		product: { description: 'Test Product' },
		packagingType: { typeCode: 'BOX', description: 'Box' },
		packagedIdentifier: 'ID123',
		nvdForCustoms: true,
		nvdForCarriage: true,
		grossWeight: 50,
		dimensions: { length: 20, width: 15, height: 10 },
		pieceQuantity: 1,
		slac: 0,
		shippingMarks: '',
		textualHandlingInstructions: '',
		containedPieces: [],
		containedItems: [],
	};

	const mockPieceItems: PieceItem[] = [
		{
			product: { description: 'Test Item' },
			itemQuantity: 1,
			weight: 10,
		},
	];

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [FormsModule, MatRadioModule, NoopAnimationsModule, TranslateModule.forRoot(), SliPieceItemComponent],
			providers: [provideHttpClient(withInterceptorsFromDi())],
			schemas: [CUSTOM_ELEMENTS_SCHEMA],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceItemComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with default values', () => {
			expect(component.pieceType).toBe('');
			expect(component.piece).toBeNull();
			expect(component.isContained).toBe('no');
		});

		it('should accept pieceType input', () => {
			component.pieceType = 'general';
			expect(component.pieceType).toBe('general');
		});
	});

	describe('ngOnChanges', () => {
		it('should set isContained to "no" when piece has no containedPieces', () => {
			// Setup
			component.isContained = 'yes'; // Start with a different value
			const changes = {
				piece: new SimpleChange(null, mockPiece, true),
			};

			// Execute
			component.ngOnChanges(changes);

			// Verify
			expect(component.isContained).toBe('no');
		});

		it('should set isContained to "yes" when piece has containedPieces', () => {
			// Create a piece with a non-empty containedPieces array
			const pieceWithContained: Piece = {
				...mockPiece,
				containedPieces: [
					{
						upid: 'CONTAINED1',
						type: 'Piece',
						product: { description: 'Contained Product' },
						packagingType: { typeCode: 'BOX', description: 'Box' },
						grossWeight: 10,
						dimensions: { length: 5, width: 5, height: 5 },
						pieceQuantity: 1,
						slac: 0,
						containedPieces: [],
						containedItems: [],
					},
				],
			};

			// Setup
			component.isContained = 'no';
			const changes = {
				piece: new SimpleChange(null, pieceWithContained, true),
			};

			// Execute
			component.piece = pieceWithContained;
			component.ngOnChanges(changes);

			// Verify
			expect(component.isContained).toBe('yes');
		});

		it('should not change isContained when changes do not include piece', () => {
			// Setup
			component.isContained = 'specific-value';
			const changes = {
				pieceType: new SimpleChange('', 'general', true),
			};

			// Execute
			component.ngOnChanges(changes);

			// Verify
			expect(component.isContained).toBe('specific-value');
		});
	});

	describe('Accessor Methods', () => {
		it('should return empty array from getPieceItemList when pieceItemPanel is undefined', () => {
			// The ViewChild reference will be undefined initially
			component.pieceItemPanel = undefined as any;
			expect(component.getPieceItemList()).toEqual([]);
		});

		it('should return array from getPieceItemList when pieceItemPanel is defined', () => {
			// Create a spy object to simulate the child component
			const panelSpy = jasmine.createSpyObj('PieceItemPanelComponent', ['getPieceItemList']);
			panelSpy.getPieceItemList.and.returnValue(mockPieceItems);

			// Set the spy on the component
			component.pieceItemPanel = panelSpy;

			// Verify the method delegates correctly
			expect(component.getPieceItemList()).toEqual(mockPieceItems);
			expect(panelSpy.getPieceItemList).toHaveBeenCalled();
		});

		it('should return empty array from getPieceInList when pieceInPanel is undefined', () => {
			component.pieceInPanel = undefined;
			expect(component.getPieceInList()).toEqual([]);
		});

		it('should return array from getPieceInList when pieceInPanel is defined', () => {
			// Create a spy object to simulate the child component
			const panelSpy = jasmine.createSpyObj('PieceInPanelComponent', ['getPieceInList']);
			panelSpy.getPieceInList.and.returnValue([mockPiece]);

			// Set the spy on the component
			component.pieceInPanel = panelSpy;

			// Verify the method delegates correctly
			expect(component.getPieceInList()).toEqual([mockPiece]);
			expect(panelSpy.getPieceInList).toHaveBeenCalled();
		});
	});

	describe('Conditional Rendering', () => {
		it('should handle general piece type', () => {
			// Setup with general piece type
			component.pieceType = 'general';
			component.isContained = 'no';
			fixture.detectChanges();

			// Verify the component handles this configuration without errors
			expect(component.pieceType).toBe('general');
			expect(component.isContained).toBe('no');
		});

		it('should handle non-general piece type', () => {
			// Setup with non-general piece type
			component.pieceType = 'dg';
			fixture.detectChanges();

			// Verify the component handles this configuration without errors
			expect(component.pieceType).toBe('dg');
		});
	});

	describe('Data Binding', () => {
		it('should handle piece input binding', () => {
			// Setup
			component.piece = mockPiece;
			fixture.detectChanges();

			expect(component.piece).toBe(mockPiece);
		});

		it('should handle null piece input', () => {
			// Setup
			component.piece = null;
			fixture.detectChanges();

			// Simply verify no errors occur
			expect(component.piece).toBeNull();
		});
	});
});
