import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliConsigneeComponent } from './sli-consignee.component';
import { TranslateModule } from '@ngx-translate/core';
import { ShipmentParty } from '../../models/shipment-party.model';
import { MatDialog } from '@angular/material/dialog';
import { ChangeDetectorRef } from '@angular/core';
import { of } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('SliConsigneeComponent', () => {
	let component: SliConsigneeComponent;
	let fixture: ComponentFixture<SliConsigneeComponent>;
	let mockMatDialog: jasmine.SpyObj<MatDialog>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;

	// Mock data
	const mockShipmentParty: ShipmentParty = {
		companyName: 'CIeNET Technology',
		contactName: '<PERSON>',
		countryCode: 'China',
		regionCode: 'Beijing',
		cityCode: 'Beijing',
		textualPostCode: '10000',
		locationName: "Qi Ming Int'l Blag. 12F, No.101, Wangjing Lize Zhongy...",
		phoneNumber: '13900000000',
		emailAddress: '<EMAIL>',
		companyType: 'CNE',
	};

	const mockDialogResult = {
		companyName: 'New Company',
		persons: [
			{
				contactRole: 'CUSTOMER_CONTACT',
				contactName: 'New Contact',
				phoneNumber: '12345678',
				emailAddress: '<EMAIL>',
			},
		],
		countryCode: 'USA',
		regionCode: 'California',
		cityCode: 'San Francisco',
		textualPostCode: '94105',
		locationName: '123 Main St',
		partyRole: 'CNE',
	};

	beforeEach(async () => {
		// Create spy objects for dependencies
		mockMatDialog = jasmine.createSpyObj<MatDialog>('MatDialog', ['open']);
		mockChangeDetectorRef = jasmine.createSpyObj<ChangeDetectorRef>('ChangeDetectorRef', ['markForCheck']);

		// Configure mock dialog to return an observable
		const mockDialogRef = {
			afterClosed: jasmine.createSpy('afterClosed').and.returnValue(of(null)),
		};
		mockMatDialog.open.and.returnValue(mockDialogRef as any);

		await TestBed.configureTestingModule({
			imports: [SliConsigneeComponent, TranslateModule.forRoot(), NoopAnimationsModule],
			providers: [
				{ provide: MatDialog, useValue: mockMatDialog },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SliConsigneeComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create the component', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with null shipmentParty', () => {
			expect(component.shipmentParty).toBeNull();
		});

		it('should have title input property', () => {
			component.title = '';
			expect(component.title).toBe('');
		});
	});

	describe('Input Handling', () => {
		it('should update shipmentParty when shipmentParty Input changes', () => {
			// Set initial value
			component.shipmentParty = null;

			// Trigger ngOnChanges with new input
			component.shipmentParty = mockShipmentParty;

			expect(component.shipmentParty).not.toBeNull();
			expect(component.shipmentParty).toEqual(mockShipmentParty);
		});
	});

	describe('getOrgList Method', () => {
		it('should open dialog with correct parameters', () => {
			component.getOrgList();

			expect(mockMatDialog.open).toHaveBeenCalledWith(jasmine.any(Function), {
				width: '400px',
				data: { orgType: '' },
			});
		});

		it('should not update shipmentParty when dialog is closed without result', () => {
			// Set initial value
			component.shipmentParty = mockShipmentParty;

			// Configure dialog to return null
			const mockDialogRef = {
				afterClosed: jasmine.createSpy('afterClosed').and.returnValue(of(null)),
			};
			mockMatDialog.open.and.returnValue(mockDialogRef as any);

			component.getOrgList();

			expect(component.shipmentParty).toEqual(mockShipmentParty);
		});

		it('should update shipmentParty when dialog returns a result', () => {
			// Set initial value
			component.shipmentParty = mockShipmentParty;

			// Configure dialog to return a result
			const mockDialogRef = {
				afterClosed: jasmine.createSpy('afterClosed').and.returnValue(of(mockDialogResult)),
			};
			mockMatDialog.open.and.returnValue(mockDialogRef as any);

			component.getOrgList();

			// Verify shipmentParty is updated with correct values
			expect(component.shipmentParty).toEqual({
				companyName: 'New Company',
				contactName: 'New Contact',
				countryCode: 'USA',
				regionCode: 'California',
				cityCode: 'San Francisco',
				textualPostCode: '94105',
				locationName: '123 Main St',
				phoneNumber: '12345678',
				emailAddress: '<EMAIL>',
				companyType: 'CNE',
			});
		});
	});

	describe('getFormData Method', () => {
		it('should return null when shipmentParty is null', () => {
			component.shipmentParty = null;
			expect(component.getFormData()).toBeNull();
		});

		it('should return shipmentParty when it has value', () => {
			component.shipmentParty = mockShipmentParty;
			expect(component.getFormData()).toEqual(mockShipmentParty);
		});
	});
});
