import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SliPieceFormComponent } from './sli-piece-form.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { of } from 'rxjs';
import { CodeName } from '@shared/models/code-name.model';
import { DropDownType } from '@shared/models/dropdown-type.model';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { SimpleChange } from '@angular/core';
import { Piece } from '../../models/piece/piece.model';

describe('SliPieceFormComponent', () => {
	let component: SliPieceFormComponent;
	let fixture: ComponentFixture<SliPieceFormComponent>;
	let sliCreateRequestServiceSpy: jasmine.SpyObj<SliCreateRequestService>;
	const mockPackagingTypes: CodeName[] = [
		{ code: 'BOX', name: 'Box' },
		{ code: 'PALLET', name: 'Pallet' },
		{ code: 'CRATE', name: 'Crate' },
	];

	const mockPiece: Piece = {
		upid: 'PIECE123',
		type: 'Piece',
		product: {
			description: 'Test Product',
			hsCommodityDescription: 'HS Test Description',
		},
		packagingType: {
			typeCode: 'BOX',
			description: 'Box',
		},
		packagedIdentifier: 'PKG001',
		nvdForCustoms: true,
		nvdForCarriage: true,
		grossWeight: 50.5,
		dimensions: {
			length: 20.5,
			width: 15.5,
			height: 10.5,
		},
		pieceQuantity: 1,
		slac: 0,
		shippingMarks: 'Test Marks',
		textualHandlingInstructions: 'Test Instructions',
		containedPieces: [],
		containedItems: [],
	};

	beforeEach(async () => {
		sliCreateRequestServiceSpy = jasmine.createSpyObj('SliCreateRequestService', ['getPackingTypes']);
		sliCreateRequestServiceSpy.getPackingTypes.and.returnValue(of(mockPackagingTypes));

		await TestBed.configureTestingModule({
			imports: [
				SliPieceFormComponent,
				TranslateModule.forRoot(),
				ReactiveFormsModule,
				NoopAnimationsModule,
				MatAutocompleteModule,
				MatFormFieldModule,
				MatInputModule,
				MatSelectModule,
			],
			providers: [
				provideHttpClient(withInterceptorsFromDi()),
				{ provide: SliCreateRequestService, useValue: sliCreateRequestServiceSpy },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceFormComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize the form with default values', () => {
		// Assert form controls exist and have default values
		expect(component.sliPieceForm.get('productDescription')).toBeTruthy();
		expect(component.sliPieceForm.get('packagingType')).toBeTruthy();
		expect(component.sliPieceForm.get('grossWeight')).toBeTruthy();
		expect(component.sliPieceForm.get('dimLength')).toBeTruthy();
		expect(component.sliPieceForm.get('dimWidth')).toBeTruthy();
		expect(component.sliPieceForm.get('dimHeight')).toBeTruthy();

		// Check default values for dropdown fields
		expect(component.sliPieceForm.get('nvdForCustoms')?.value).toBe('NCV');
		expect(component.sliPieceForm.get('nvdForCarriage')?.value).toBe('NVD');
	});

	it('should load packaging types on init', () => {
		// Assert the service was called
		expect(sliCreateRequestServiceSpy.getPackingTypes).toHaveBeenCalled();

		// Assert the data was stored in the component
		expect(component.packagingTypes).toEqual(mockPackagingTypes);
		expect(component.filteredPackagingTypes).toEqual(mockPackagingTypes);
	});

	it('should validate required fields', () => {
		// Initially form should be invalid
		expect(component.sliPieceForm.valid).toBeFalsy();

		// Set required values
		component.sliPieceForm.patchValue({
			productDescription: 'Test Product',
			packagingType: 'BOX',
			grossWeight: '10.5',
			dimLength: '20.0',
			dimWidth: '15.0',
			dimHeight: '10.0',
			pieceQuantity: '1',
		});

		// Form should now be valid
		expect(component.sliPieceForm.valid).toBeTruthy();
	});

	it('should validate numeric fields with decimal pattern', () => {
		const grossWeightControl = component.sliPieceForm.get('grossWeight');

		// Invalid values
		grossWeightControl?.setValue('abc');
		expect(grossWeightControl?.valid).toBeFalsy();

		grossWeightControl?.setValue('10.25'); // More than 1 decimal place
		expect(grossWeightControl?.valid).toBeFalsy();

		// Valid values
		grossWeightControl?.setValue('10');
		expect(grossWeightControl?.valid).toBeTruthy();

		grossWeightControl?.setValue('10.5');
		expect(grossWeightControl?.valid).toBeTruthy();
	});

	it('should filter packaging types based on search input', () => {
		// Simulate user typing in the autocomplete
		component.sliPieceForm.get('packagingType')?.setValue('Box');
		fixture.detectChanges();

		// Should filter to only show 'Box'
		expect(component.filteredPackagingTypes.length).toBe(1);
		expect(component.filteredPackagingTypes[0].code).toBe('BOX');

		// Try another search
		component.sliPieceForm.get('packagingType')?.setValue('Pal');
		fixture.detectChanges();

		// Should filter to only show 'Pallet'
		expect(component.filteredPackagingTypes.length).toBe(1);
		expect(component.filteredPackagingTypes[0].code).toBe('PALLET');

		// Empty search should show all
		component.sliPieceForm.get('packagingType')?.setValue('');
		fixture.detectChanges();

		expect(component.filteredPackagingTypes.length).toBe(mockPackagingTypes.length);
	});

	it('should display packaging type name correctly', () => {
		// Test with existing code
		expect(component.displayPackagingTypeName('BOX')).toBe('Box');

		// Test with non-existent code
		expect(component.displayPackagingTypeName('NONEXISTENT')).toBe('');
	});

	it('should initialize dropdown values correctly', () => {
		expect(component.nvdForCustoms).toEqual([DropDownType.NCV, DropDownType.YES]);
		expect(component.nvdForCarriage).toEqual([DropDownType.NVD, DropDownType.YES]);
	});

	describe('ngOnChanges', () => {
		it('should call fillPieceForm when piece input changes', () => {
			// Spy on the fillPieceForm method
			spyOn(component, 'fillPieceForm');

			// Create a simple change object with piece
			const changes = {
				piece: new SimpleChange(null, mockPiece, true),
			};

			// Call ngOnChanges with the changes
			component.ngOnChanges(changes);

			// Verify fillPieceForm was called
			expect(component.fillPieceForm).toHaveBeenCalled();
		});

		it('should not call fillPieceForm when other inputs change', () => {
			// Spy on the fillPieceForm method
			spyOn(component, 'fillPieceForm');

			// Create a simple change object without piece
			const changes = {
				someOtherInput: new SimpleChange(null, 'value', true),
			};

			// Call ngOnChanges with the changes
			component.ngOnChanges(changes);

			// Verify fillPieceForm was not called
			expect(component.fillPieceForm).not.toHaveBeenCalled();
		});
	});

	describe('fillPieceForm', () => {
		it('should correctly populate form when piece input is provided', () => {
			// Set the piece input
			component.piece = mockPiece;

			// Call the method
			component.fillPieceForm();

			// Verify form was populated correctly
			expect(component.sliPieceForm.get('productDescription')?.value).toBe(mockPiece.product.description);
			expect(component.sliPieceForm.get('hsCommodityDescription')?.value).toBe(mockPiece.product.hsCommodityDescription);
			expect(component.sliPieceForm.get('packagingType')?.value).toBe(mockPiece.packagingType.typeCode);
			expect(component.sliPieceForm.get('packagedIdentifier')?.value).toBe(mockPiece.packagedIdentifier);
			expect(component.sliPieceForm.get('grossWeight')?.value).toBe(mockPiece.grossWeight);
			expect(component.sliPieceForm.get('dimLength')?.value).toBe(mockPiece.dimensions.length);
			expect(component.sliPieceForm.get('dimWidth')?.value).toBe(mockPiece.dimensions.width);
			expect(component.sliPieceForm.get('dimHeight')?.value).toBe(mockPiece.dimensions.height);
			expect(component.sliPieceForm.get('nvdForCustoms')?.value).toBe(DropDownType.NCV); // True maps to NCV
			expect(component.sliPieceForm.get('nvdForCarriage')?.value).toBe(DropDownType.NVD); // True maps to NVD
			expect(component.sliPieceForm.get('upid')?.value).toBe(mockPiece.upid);
			expect(component.sliPieceForm.get('shippingMarks')?.value).toBe(mockPiece.shippingMarks);
			expect(component.sliPieceForm.get('textualHandlingInstructions')?.value).toBe(mockPiece.textualHandlingInstructions);
		});

		it('should use default values when piece input properties are missing', () => {
			// Set an incomplete piece
			component.piece = {
				type: 'Piece',
				product: { description: 'Partial Product' },
				packagingType: { typeCode: '', description: '' },
				grossWeight: 0,
				dimensions: { length: 0, width: 0, height: 0 },
				pieceQuantity: 1,
				slac: 0,
				containedPieces: [],
				containedItems: [],
			} as Piece;

			// Call the method
			component.fillPieceForm();

			// Verify form uses defaults for missing values
			expect(component.sliPieceForm.get('productDescription')?.value).toBe('Partial Product');
			expect(component.sliPieceForm.get('packagedIdentifier')?.value).toBe('');
			expect(component.sliPieceForm.get('upid')?.value).toBe('');
		});

		it('should handle null piece input', () => {
			// Set piece to null
			component.piece = null;

			// Call the method
			component.fillPieceForm();

			// Verify form uses defaults
			expect(component.sliPieceForm.get('productDescription')?.value).toBe('');
			expect(component.sliPieceForm.get('packagingType')?.value).toBe('');
			expect(component.sliPieceForm.get('grossWeight')?.value).toBe('');
		});
	});

	describe('getFormData', () => {
		it('should return null when form is invalid and ignore is false', () => {
			// Ensure form is invalid (it should be by default)
			expect(component.sliPieceForm.valid).toBeFalsy();

			// Call getFormData without ignore flag
			const result = component.getFormData();

			// Should return null
			expect(result).toBeNull();
		});

		it('should return form data when form is valid', () => {
			// Make the form valid
			component.sliPieceForm.patchValue({
				productDescription: 'Test Product',
				packagingType: 'BOX',
				grossWeight: '10.5',
				dimLength: '20.0',
				dimWidth: '15.0',
				dimHeight: '10.0',
			});

			// Form should now be valid
			expect(component.sliPieceForm.valid).toBeTruthy();

			// Call getFormData
			const result = component.getFormData() as any;

			// Verify result
			expect(result).not.toBeNull();
			expect(result.type).toBe('Piece');
			expect(result.product.description).toBe('Test Product');
			expect(result.packagingType.typeCode).toBe('BOX');
			expect(result.packagingType.description).toBe('Box');
			expect(result.grossWeight).toBe(10.5);
			expect(result.dimensions.length).toBe(20.0);
			expect(result.dimensions.width).toBe(15.0);
			expect(result.dimensions.height).toBe(10.0);
			expect(result.nvdForCustoms).toBe(true);
			expect(result.nvdForCarriage).toBe(true);
		});

		it('should return form data even when form is invalid if ignore flag is true', () => {
			// Ensure form is invalid (it should be by default)
			expect(component.sliPieceForm.valid).toBeFalsy();

			// Set some values but keep form invalid
			component.sliPieceForm.patchValue({
				productDescription: '', // Required field is empty
				packagingType: 'BOX',
				grossWeight: '10.5',
			});

			// Form should still be invalid
			expect(component.sliPieceForm.valid).toBeFalsy();

			// Call getFormData with ignore flag
			const result = component.getFormData(true) as any;

			// Should return data
			expect(result).not.toBeNull();
			expect(result.packagingType.typeCode).toBe('BOX');
		});

		it('should correctly map dropdown values to boolean values', () => {
			// Set the form values
			component.sliPieceForm.patchValue({
				productDescription: 'Test Product',
				packagingType: 'BOX',
				grossWeight: '10.5',
				dimLength: '20.0',
				dimWidth: '15.0',
				dimHeight: '10.0',
				nvdForCustoms: DropDownType.NCV, // Should map to true
				nvdForCarriage: DropDownType.YES, // Should map to false
			});

			// Get the form data
			const result = component.getFormData(true) as any;

			// Check the mapping
			expect(result.nvdForCustoms).toBe(true); // 'NCV' should map to true
			expect(result.nvdForCarriage).toBe(false); // 'YES' should map to false

			// Update the form with opposite values
			component.sliPieceForm.patchValue({
				nvdForCustoms: DropDownType.YES, // Should map to false
				nvdForCarriage: DropDownType.NVD, // Should map to true
			});

			// Get the form data again
			const updatedResult = component.getFormData(true) as any;

			// Check the updated mapping
			expect(updatedResult.nvdForCustoms).toBe(false); // 'YES' should map to false
			expect(updatedResult.nvdForCarriage).toBe(true); // 'NVD' should map to true
		});
	});
});
