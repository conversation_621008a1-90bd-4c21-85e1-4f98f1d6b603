.orll-piece-item-panel {
	margin-top: 20px;

	&__total-items {
		color: var(--iata-grey-600);
		font-size: 20px;
		margin-right: 20px;
	}

	&__add-button {
		color: var(--iata-blue-primary) !important;
		border: 1px solid var(--iata-blue-primary);
	}

	&__desc,
	&__package {
		&.font {
			font-size: 16px;
		}
		text-align: left;
		width: 100%;

		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		padding: 0 10px;
	}

	.unit {
		margin-right: 5px;
		font-size: 14px;
		color: var(--iata-black);
	}

	.piece-item-content {
		display: flex;
		width: 100%;
		justify-content: space-between;
		align-items: center;
		padding: 10px 0;

		.item-info {
			display: flex;
			flex-grow: 1;
			flex-wrap: wrap;
			width: 100%;

			.item-description,
			.item-weight,
			.item-quantity {
				word-break: break-word;
				white-space: normal;
				padding: 0 10px;
				line-height: 1.5;
			}

			.value {
				color: var(--iata-blue-primary);
			}
		}
	}

	::ng-deep {
		.mat-expansion-panel-header-title {
			flex-direction: column;
			justify-content: center;
			color: var(--iata-grey-600);
			flex-grow: 2;
			min-width: 0;
		}

		.mat-expansion-panel-header-description {
			justify-content: flex-end;
			flex-grow: 1;
		}
	}

	mat-list-item {
		height: auto !important;
		min-height: 48px;
		::ng-deep .mat-list-item-content {
			height: auto !important;
			padding: 8px 0 !important;
		}
	}
}
