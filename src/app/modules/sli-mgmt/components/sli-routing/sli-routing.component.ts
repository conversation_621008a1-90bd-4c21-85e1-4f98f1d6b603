import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { TranslateModule } from '@ngx-translate/core';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { CodeName } from '@shared/models/code-name.model';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { CommonModule } from '@angular/common';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { startWith } from 'rxjs';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';

@Component({
	selector: 'orll-sli-routing',
	templateUrl: './sli-routing.component.html',
	styleUrl: './sli-routing.component.scss',
	changeDetection: ChangeDetectionStrategy.OnPush,
	imports: [
		MatInputModule,
		MatSelectModule,
		MatIconModule,
		TranslateModule,
		ReactiveFormsModule,
		FormsModule,
		MatFormFieldModule,
		CommonModule,
		MatAutocompleteModule,
	],
})
export class SliRoutingComponent extends DestroyRefComponent implements OnInit {
	airports: CodeName[] = [];
	filteredDepartureAirports: CodeName[] = [];
	filteredArrivalAirports: CodeName[] = [];

	sliRoutingForm: FormGroup = new FormGroup({
		departureLocation: new FormControl<string>('', [Validators.required]),
		arrivalLocation: new FormControl<string>('', [Validators.required]),
		shippingInfo: new FormControl<string>(''),
	});

	constructor(private readonly sliCreateRequestService: SliCreateRequestService) {
		super();
	}

	ngOnInit(): void {
		this.initRefData();
		this.setupAutocomplete();
	}

	private initRefData(): void {
		this.sliCreateRequestService.getAirports().subscribe((airports: CodeName[]) => {
			this.airports = airports;
			this.filteredDepartureAirports = airports;
		});
	}

	private setupAutocomplete(): void {
		this.sliRoutingForm
			.get('departureLocation')
			?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef), startWith(''))
			.subscribe((search) => {
				this.filteredDepartureAirports = this.filterAirports(search);
			});

		this.sliRoutingForm
			.get('arrivalLocation')
			?.valueChanges.pipe(takeUntilDestroyed(this.destroyRef), startWith(''))
			.subscribe((search) => {
				this.filteredArrivalAirports = this.filterAirports(search);
			});
	}

	filterAirports(search: string): CodeName[] {
		return this.airports.filter((airport) => airport.code.toLowerCase().includes(search.toLowerCase().trim()));
	}

	displayAirportName = (code: string): string => {
		const airport = this.airports.find((item) => item.code === code);
		return airport?.name ?? '';
	};

	// eslint-disable-next-line
	getFormData(ignore?: boolean): {} | null {
		if (!ignore && this.sliRoutingForm.invalid) {
			return null;
		}
		return {
			departureLocation: this.sliRoutingForm.value.departureLocation!,
			arrivalLocation: this.sliRoutingForm.value.arrivalLocation!,
			shippingInfo: this.sliRoutingForm.value.shippingInfo,
		};
	}
}
