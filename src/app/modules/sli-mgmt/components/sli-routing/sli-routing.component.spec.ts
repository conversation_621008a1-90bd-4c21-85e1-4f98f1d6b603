import { ComponentFixture, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { SliRoutingComponent } from './sli-routing.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { TranslateModule } from '@ngx-translate/core';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { of } from 'rxjs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('SliRoutingComponent', () => {
	let component: SliRoutingComponent;
	let fixture: ComponentFixture<SliRoutingComponent>;
	let mockSliCreateRequestService: jasmine.SpyObj<SliCreateRequestService>;

	// Mock data
	const mockAirports: CodeName[] = [
		{ code: 'JF<PERSON>', name: 'John <PERSON> International Airport' },
		{ code: 'LAX', name: 'Los Angeles International Airport' },
		{ code: 'ORD', name: "O'Hare International Airport" },
		{ code: 'LHR', name: 'London Heathrow Airport' },
	];

	beforeEach(async () => {
		mockSliCreateRequestService = jasmine.createSpyObj<SliCreateRequestService>('SliCreateRequestService', ['getAirports']);
		mockSliCreateRequestService.getAirports.and.returnValue(of(mockAirports));

		await TestBed.configureTestingModule({
			imports: [
				SliRoutingComponent,
				TranslateModule.forRoot(),
				NoopAnimationsModule, // Required for Material components
			],
			providers: [
				{ provide: SliCreateRequestService, useValue: mockSliCreateRequestService },
				provideHttpClient(withInterceptorsFromDi()),
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SliRoutingComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize with empty form values', () => {
			expect(component.sliRoutingForm.get('departureLocation')?.value).toBe('');
			expect(component.sliRoutingForm.get('arrivalLocation')?.value).toBe('');
			expect(component.sliRoutingForm.get('shippingInfo')?.value).toBe('');
		});

		it('should call initRefData on initialization', () => {
			const initRefDataSpy = spyOn<any>(component, 'initRefData').and.callThrough();
			component.ngOnInit();
			expect(initRefDataSpy).toHaveBeenCalledTimes(1);
		});

		it('should call setupAutocomplete on initialization', () => {
			const setupAutocompleteSpy = spyOn<any>(component, 'setupAutocomplete').and.callThrough();
			component.ngOnInit();
			expect(setupAutocompleteSpy).toHaveBeenCalledTimes(1);
		});
	});

	describe('Reference Data Loading', () => {
		it('should fetch airports and assign them to component property', fakeAsync(() => {
			tick();

			// Verify results
			expect(mockSliCreateRequestService.getAirports).toHaveBeenCalledTimes(1);
			expect(component.airports).toEqual(mockAirports);
			expect(component.filteredDepartureAirports).toEqual(mockAirports);
		}));

		it('should handle empty airports response', fakeAsync(() => {
			mockSliCreateRequestService.getAirports.and.returnValue(of([]));

			component['initRefData']();
			tick();

			expect(component.airports).toEqual([]);
			expect(component.filteredDepartureAirports).toEqual([]);
		}));
	});

	describe('Autocomplete Functionality', () => {
		it('should filter departure airports based on input', fakeAsync(() => {
			// Setup
			component.airports = mockAirports;
			component.filteredDepartureAirports = mockAirports;
			fixture.detectChanges();

			// Trigger filter
			const departureControl = component.sliRoutingForm.get('departureLocation');
			departureControl?.setValue('JF');
			tick();

			// Verify filtering
			expect(component.filteredDepartureAirports.length).toBe(1);
			expect(component.filteredDepartureAirports[0].code).toBe('JFK');
		}));

		it('should filter arrival airports based on input', fakeAsync(() => {
			// Setup
			component.airports = mockAirports;
			component.filteredArrivalAirports = mockAirports;
			fixture.detectChanges();

			// Trigger filter
			const arrivalControl = component.sliRoutingForm.get('arrivalLocation');
			arrivalControl?.setValue('LH');
			tick();

			// Verify filtering
			expect(component.filteredArrivalAirports.length).toBe(1);
			expect(component.filteredArrivalAirports[0].code).toBe('LHR');
		}));

		it('should correctly display airport name', () => {
			component.airports = mockAirports;
			expect(component.displayAirportName('JFK')).toBe('John F Kennedy International Airport');
			expect(component.displayAirportName('INVALID')).toBe('');
		});
	});

	describe('Form Validation', () => {
		it('should validate required fields', () => {
			const form = component.sliRoutingForm;
			expect(form.valid).toBeFalsy();

			// Set required fields
			form.get('departureLocation')?.setValue('JFK');
			form.get('arrivalLocation')?.setValue('LAX');

			expect(form.valid).toBeTruthy();
		});

		it('should validate departureLocation field', () => {
			const departureControl = component.sliRoutingForm.get('departureLocation');
			expect(departureControl?.valid).toBeFalsy();
			expect(departureControl?.hasError('required')).toBeTruthy();

			departureControl?.setValue('JFK');
			expect(departureControl?.valid).toBeTruthy();
		});

		it('should validate arrivalLocation field', () => {
			const arrivalControl = component.sliRoutingForm.get('arrivalLocation');
			expect(arrivalControl?.valid).toBeFalsy();
			expect(arrivalControl?.hasError('required')).toBeTruthy();

			arrivalControl?.setValue('LAX');
			expect(arrivalControl?.valid).toBeTruthy();
		});
	});

	describe('getFormData Method', () => {
		it('should return null when form is invalid', () => {
			// Form is initially invalid
			expect(component.getFormData()).toBeNull();
		});

		it('should return form data when form is valid', () => {
			// Set all required fields
			component.sliRoutingForm.patchValue({
				departureLocation: 'JFK',
				arrivalLocation: 'LAX',
				shippingInfo: 'Test shipping info',
			});

			const result = component.getFormData();
			expect(result).toEqual({
				departureLocation: 'JFK',
				arrivalLocation: 'LAX',
				shippingInfo: 'Test shipping info',
			});
		});

		it('should return form data when shippingInfo is empty', () => {
			component.sliRoutingForm.patchValue({
				departureLocation: 'JFK',
				arrivalLocation: 'LAX',
				shippingInfo: '',
			});

			const result = component.getFormData();
			expect(result).toEqual({
				departureLocation: 'JFK',
				arrivalLocation: 'LAX',
				shippingInfo: '',
			});
		});

		it('should return data even when form is invalid if ignore flag is true', () => {
			// Set only one required field
			component.sliRoutingForm.patchValue({
				departureLocation: 'JFK',
				arrivalLocation: '',
				shippingInfo: 'Test shipping info',
			});

			// Should return null without ignore flag
			expect(component.getFormData()).toBeNull();

			// Should return data with ignore flag
			const result = component.getFormData(true);
			expect(result).not.toBeNull();
			expect(result).toEqual({
				departureLocation: 'JFK',
				arrivalLocation: '',
				shippingInfo: 'Test shipping info',
			});
		});
	});
});
