import { ComponentFixture, TestBed, fakeAsync, tick, discardPeriodicTasks } from '@angular/core/testing';
import { SliPieceListComponent } from './sli-piece-list.component';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { SliCreateRequestService } from '../../services/sli-create-request.service';
import { of, throwError } from 'rxjs';
import { CodeName } from '@shared/models/code-name.model';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { PieceList } from '../../models/piece/piece-list.model';
import { ChangeDetectorRef } from '@angular/core';

describe('SliPieceListComponent', () => {
	let component: SliPieceListComponent;
	let fixture: ComponentFixture<SliPieceListComponent>;
	let mockSliCreateRequestService: jasmine.SpyObj<SliCreateRequestService>;
	let mockChangeDetectorRef: jasmine.SpyObj<ChangeDetectorRef>;

	// Mock data
	const mockCurrencies = ['USD', 'EUR', 'GBP'];
	const mockIncoterms: CodeName[] = [
		{ code: 'EXW', name: 'Ex Works' },
		{ code: 'FCA', name: 'Free Carrier' },
		{ code: 'CPT', name: 'Carriage Paid To' },
	];
	const mockPieceListResponse = {
		rows: [
			{
				type: 'Piece',
				pieceId: 'TEST123',
				productDescription: '123',
				packagingType: '456',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			},
			{
				type: 'Piece',
				pieceId: 'TEST456',
				productDescription: '456',
				packagingType: '789',
				grossWeight: 10,
				dimensions: {
					length: 10,
					width: 10,
					height: 10,
				},
				pieceQuantity: 1,
				slac: 0,
			},
		] as PieceList[],
		total: 2,
	};

	// Helper function to fill form with valid data
	function fillFormWithValidData(): void {
		component.sliPieceListForm.patchValue({
			goodsDescription: 'Test goods',
			totalGrossWeight: '100',
			dimLength: '50',
			dimWidth: '30',
			dimHeight: '20',
			declaredValueForCustoms: 'NCV',
			declaredValueForCarriage: 'NVD',
			insuredAmount: 'NIL',
			declaredValueForCustomsCurrency: 'USD',
			declaredValueForCarriageCurrency: 'EUR',
			insuredAmountCurrency: 'GBP',
			textualHandlingInstructions: 'Handle with care',
			weightValuationIndicator: 'Prepaid',
			incoterms: 'FCA',
		});
	}

	beforeEach(async () => {
		// Create spy objects with all required methods
		mockSliCreateRequestService = jasmine.createSpyObj<SliCreateRequestService>('SliCreateRequestService', [
			'getCurrencies',
			'getIncoterms',
			'getPieceList',
		]);
		mockChangeDetectorRef = jasmine.createSpyObj<ChangeDetectorRef>('ChangeDetectorRef', ['markForCheck']);

		// Configure mock return values
		mockSliCreateRequestService.getCurrencies.and.returnValue(of(mockCurrencies));
		mockSliCreateRequestService.getIncoterms.and.returnValue(of(mockIncoterms));
		mockSliCreateRequestService.getPieceList.and.returnValue(of(mockPieceListResponse));

		await TestBed.configureTestingModule({
			imports: [
				SliPieceListComponent,
				TranslateModule.forRoot(),
				ReactiveFormsModule,
				NoopAnimationsModule,
				MatFormFieldModule,
				MatInputModule,
				MatSelectModule,
			],
			providers: [
				{ provide: SliCreateRequestService, useValue: mockSliCreateRequestService },
				{ provide: ChangeDetectorRef, useValue: mockChangeDetectorRef },
			],
		}).compileComponents();

		fixture = TestBed.createComponent(SliPieceListComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	describe('Component Initialization', () => {
		it('should create the component', () => {
			expect(component).toBeTruthy();
		});

		it('should initialize the form with default values', () => {
			expect(component.sliPieceListForm).toBeDefined();
			expect(component.sliPieceListForm.get('goodsDescription')?.value).toBe('');
			expect(component.sliPieceListForm.get('totalGrossWeight')?.value).toBe('');
			expect(component.sliPieceListForm.get('dimLength')?.value).toBe('');
			expect(component.sliPieceListForm.get('dimWidth')?.value).toBe('');
			expect(component.sliPieceListForm.get('dimHeight')?.value).toBe('');
			expect(component.sliPieceListForm.get('declaredValueForCustoms')?.value).toBe('NCV');
			expect(component.sliPieceListForm.get('declaredValueForCarriage')?.value).toBe('NVD');
			expect(component.sliPieceListForm.get('insuredAmount')?.value).toBe('NIL');
		});

		it('should call initRefData and setupAutocomplete on initialization', () => {
			const initRefDataSpy = spyOn<any>(component, 'initRefData').and.callThrough();
			const setupAutocompleteSpy = spyOn<any>(component, 'setupAutocomplete').and.callThrough();

			component.ngOnInit();

			expect(initRefDataSpy).toHaveBeenCalledTimes(1);
			expect(setupAutocompleteSpy).toHaveBeenCalledTimes(1);
		});

		it('should fetch piece list when sliNumber is provided', fakeAsync(() => {
			component.sliNumber = 'TEST-SLI-123';

			component.ngOnInit();
			tick(2000); // Wait for the setTimeout to complete

			expect(mockSliCreateRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, 'TEST-SLI-123');
			expect(component.pieceList).toEqual(mockPieceListResponse.rows);
			expect(component.totalRecords).toBe(mockPieceListResponse.total);

			discardPeriodicTasks(); // Clean up any pending timers
		}));

		it('should not fetch piece list when sliNumber is not provided', fakeAsync(() => {
			component.sliNumber = '';

			component.ngOnInit();
			tick(2000); // Wait for the setTimeout to complete

			expect(mockSliCreateRequestService.getPieceList).not.toHaveBeenCalled();

			discardPeriodicTasks(); // Clean up any pending timers
		}));
	});

	describe('Reference Data Loading', () => {
		it('should fetch currencies and incoterms on init', fakeAsync(() => {
			// Reset data to verify initialization
			component.currencies = [];
			component.incoterms = [];

			// Call method directly
			component['initRefData']();
			tick();

			// Verify service calls
			expect(mockSliCreateRequestService.getCurrencies).toHaveBeenCalled();
			expect(mockSliCreateRequestService.getIncoterms).toHaveBeenCalled();

			// Verify data assignment
			expect(component.currencies).toEqual(['', ...mockCurrencies]);
			expect(component.incoterms).toEqual(mockIncoterms);
			expect(component.filteredInsuredAmountCurrency).toEqual(['', ...mockCurrencies]);
		}));

		it('should handle empty currencies response', fakeAsync(() => {
			mockSliCreateRequestService.getCurrencies.and.returnValue(of([]));

			component['initRefData']();
			tick();

			expect(component.currencies).toEqual(['']);
			expect(component.filteredInsuredAmountCurrency).toEqual(['']);
		}));
	});

	describe('Autocomplete Functionality', () => {
		it('should filter currencies based on input', fakeAsync(() => {
			// Setup
			component.currencies = ['', 'USD', 'EUR', 'GBP'];
			fixture.detectChanges();

			const customsCurrencyControl = component.sliPieceListForm.get('declaredValueForCustomsCurrency');
			customsCurrencyControl?.setValue('US');
			tick();

			expect(component.filteredDeclaredCustomsCurrency.length).toBe(1);
			expect(component.filteredDeclaredCustomsCurrency[0]).toBe('USD');

			const carriageCurrencyControl = component.sliPieceListForm.get('declaredValueForCarriageCurrency');
			carriageCurrencyControl?.setValue('EU');
			tick();

			expect(component.filteredDeclaredCarriageCurrency.length).toBe(1);
			expect(component.filteredDeclaredCarriageCurrency[0]).toBe('EUR');

			const insuredCurrencyControl = component.sliPieceListForm.get('insuredAmountCurrency');
			insuredCurrencyControl?.setValue('GB');
			tick();

			expect(component.filteredInsuredAmountCurrency.length).toBe(1);
			expect(component.filteredInsuredAmountCurrency[0]).toBe('GBP');
		}));

		it('should handle empty search term in filterCurrency', () => {
			component.currencies = ['', 'USD', 'EUR', 'GBP'];

			const result = component.filterCurrency('');

			expect(result).toEqual(['', 'USD', 'EUR', 'GBP']);
		});

		it('should handle null search term in filterCurrency', () => {
			component.currencies = ['', 'USD', 'EUR', 'GBP'];

			const result = component.filterCurrency(null as unknown as string);

			expect(result).toEqual(['', 'USD', 'EUR', 'GBP']);
		});

		it('should filter currencies case-insensitively', () => {
			component.currencies = ['', 'USD', 'EUR', 'GBP'];

			const result = component.filterCurrency('us');

			expect(result).toEqual(['USD']);
		});
	});

	describe('Form Validation', () => {
		it('should mark form as invalid when required fields are empty', () => {
			expect(component.sliPieceListForm.valid).toBeFalsy();

			// Verify specific required field validations
			expect(component.sliPieceListForm.get('goodsDescription')?.hasError('required')).toBeTruthy();
			expect(component.sliPieceListForm.get('totalGrossWeight')?.hasError('required')).toBeTruthy();
			expect(component.sliPieceListForm.get('dimLength')?.hasError('required')).toBeTruthy();
			expect(component.sliPieceListForm.get('dimWidth')?.hasError('required')).toBeTruthy();
			expect(component.sliPieceListForm.get('dimHeight')?.hasError('required')).toBeTruthy();
		});

		it('should mark form as valid when all required fields are filled', () => {
			component.sliPieceListForm.patchValue({
				goodsDescription: 'Test goods',
				totalGrossWeight: '100',
				dimLength: '50',
				dimWidth: '30',
				dimHeight: '20',
			});

			expect(component.sliPieceListForm.valid).toBeTruthy();
		});

		it('should validate numeric fields with pattern', () => {
			const testNumericValidation = (controlName: string) => {
				const control = component.sliPieceListForm.get(controlName);

				control?.setValue('abc');
				expect(control?.valid).toBeFalsy();
				expect(control?.hasError('pattern')).toBeTruthy();

				control?.setValue('123.4');
				expect(control?.valid).toBeTruthy();
			};

			// Test all numeric fields
			testNumericValidation('totalGrossWeight');
			testNumericValidation('dimLength');
			testNumericValidation('dimWidth');
			testNumericValidation('dimHeight');
		});

		it('should validate special fields with custom patterns', () => {
			// Test declaredValueForCustoms
			const customsControl = component.sliPieceListForm.get('declaredValueForCustoms');

			// Valid special value
			customsControl?.setValue('NCV');
			expect(customsControl?.valid).toBeTruthy();

			// Valid numeric value
			customsControl?.setValue('100.50');
			expect(customsControl?.valid).toBeTruthy();

			// Invalid value
			customsControl?.setValue('INVALID');
			expect(customsControl?.valid).toBeFalsy();
		});
	});

	describe('getFormData Method', () => {
		it('should return null when form is invalid', () => {
			// Form is initially invalid
			expect(component.getFormData()).toBeNull();
		});

		it('should return form data when form is valid', () => {
			fillFormWithValidData();

			const formData = component.getFormData();
			expect(formData).not.toBeNull();
			expect(formData).toEqual(
				jasmine.objectContaining({
					goodsDescription: 'Test goods',
					totalGrossWeight: 100,
					totalDimensions: {
						length: 50,
						width: 30,
						height: 20,
					},
					textualHandlingInstructions: 'Handle with care',
					weightValuationIndicator: 'Prepaid',
					incoterms: 'FCA',
					pieces: [],
				})
			);
		});

		it('should handle numeric values in special fields', () => {
			// Fill form with valid data first
			fillFormWithValidData();

			// Override special fields with numeric values
			component.sliPieceListForm.patchValue({
				declaredValueForCustoms: '1000',
				declaredValueForCarriage: '2000',
				insuredAmount: '3000',
			});

			const formData = component.getFormData() as any;
			expect(formData.insuredAmount.numericalValue).toBe(3000);
			expect(formData.insuredAmount.currencyUnit).toBe('GBP');
			expect(formData.declaredValueForCustoms.numericalValue).toBe(1000);
			expect(formData.declaredValueForCustoms.currencyUnit).toBe('USD');
			expect(formData.declaredValueForCarriage.numericalValue).toBe(2000);
			expect(formData.declaredValueForCarriage.currencyUnit).toBe('EUR');
		});

		it('should return form data when ignore flag is true, even if form is invalid', () => {
			// Form is initially invalid
			expect(component.sliPieceListForm.valid).toBeFalsy();

			// Should return data anyway when ignore is true
			const formData = component.getFormData(true);
			expect(formData).not.toBeNull();
		});
	});

	describe('Sorting and Pagination', () => {
		it('should update pageParams when onSortChange is called with direction', () => {
			const sort: Sort = { active: 'productDescription', direction: 'asc' };

			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('productDescription');
			expect(component.pageParams.isAsc).toBe('asc');
		});

		it('should clear sort parameters when onSortChange is called with empty direction', () => {
			// First set some values
			component.pageParams.orderByColumn = 'productDescription';
			component.pageParams.isAsc = 'asc';

			// Then clear them
			const sort: Sort = { active: 'productDescription', direction: '' };
			component.onSortChange(sort);

			expect(component.pageParams.orderByColumn).toBe('');
			expect(component.pageParams.isAsc).toBe('');
		});

		it('should update pageParams and fetch data when onPageChange is called', () => {
			spyOn<any>(component, 'getPieceListPage').and.callThrough();

			const pageEvent: PageEvent & { sortField?: string; sortDirection?: string } = {
				pageIndex: 2,
				pageSize: 50,
				length: 100,
				sortField: 'grossWeight',
				sortDirection: 'desc',
			};

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(3); // pageIndex + 1
			expect(component.pageParams.pageSize).toBe(50);
			expect(component.pageParams.orderByColumn).toBe('grossWeight');
			expect(component.pageParams.isAsc).toBe('desc');
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});

		it('should update pageParams without sort parameters when onPageChange is called without sort data', () => {
			spyOn<any>(component, 'getPieceListPage').and.callThrough();

			const pageEvent: PageEvent = {
				pageIndex: 2,
				pageSize: 50,
				length: 100,
			};

			component.onPageChange(pageEvent);

			expect(component.pageParams.pageNum).toBe(3); // pageIndex + 1
			expect(component.pageParams.pageSize).toBe(50);
			expect(component['getPieceListPage']).toHaveBeenCalledWith(component.pageParams);
		});
	});

	describe('getPieceListPage Method', () => {
		it('should fetch piece list data and update component properties', fakeAsync(() => {
			component.sliNumber = 'TEST-SLI-123';
			component.dataLoading = false;
			component.pieceList = [];
			component.totalRecords = 0;

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(mockSliCreateRequestService.getPieceList).toHaveBeenCalledWith(component.pageParams, 'TEST-SLI-123');
			expect(component.pieceList).toEqual(mockPieceListResponse.rows);
			expect(component.totalRecords).toBe(mockPieceListResponse.total);
			expect(component.dataLoading).toBe(false);
		}));

		it('should handle error when fetching piece list data', fakeAsync(() => {
			mockSliCreateRequestService.getPieceList.and.returnValue(throwError(() => new Error('Test error')));

			component.sliNumber = 'TEST-SLI-123';
			component.dataLoading = false;

			component['getPieceListPage'](component.pageParams);
			tick();

			expect(component.dataLoading).toBe(false);
		}));
	});
});
