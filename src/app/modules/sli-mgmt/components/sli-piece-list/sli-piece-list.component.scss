.orll-sli-piece-list {
	.width-100 {
		width: 100%;
	}

	.width-10 {
		width: 10%;
	}

	.width-13 {
		width: 13%;
	}

	.width-5 {
		width: 5%;
	}

	.margin-auto {
		margin: auto;
	}

	.margin-b-20 {
		margin-bottom: 20px;
	}

	.margin-t-20 {
		margin-top: 20px;
	}

	.declared-value {
		margin-left: 30px;
	}

	.total-gross-weight {
		margin-right: 12px;
	}

	.total-dimensions {
		margin-left: -1px;
	}

	.col-1 {
		flex: 0 0 7% !important;
		max-width: 7% !important;
	}

	.unit {
		margin-right: 5px;
		font-size: 14px;
		color: var(--iata-black);
	}

	.incoterms {
		margin-top: -61px;
	}

	.autocomplete-arrow {
		padding: 0 5px;
	}

	::ng-deep .mat-mdc-form-field-icon-suffix {
		padding: 0;
	}
}
