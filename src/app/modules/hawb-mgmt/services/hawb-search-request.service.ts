import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { AIRPORTS } from '../../sli-mgmt/ref-data/airports.data';
import { Airport } from '../../sli-mgmt/models/airport.model';
import { HawbListObject } from '../models/hawb-list-object.model';
import { PaginationResponse } from '@shared/models/pagination-response.model';
import { AbstractAutocompleteService } from '@shared/models/autocomplete.model';
import { CodeName } from '@shared/models/code-name.model';
import { ApiService } from '@shared/services/api.service';
import { HttpClient } from '@angular/common/http';
import { HawbSearchPayload } from '../models/hawb-search-payload.model';
import { PaginationRequest } from '@shared/models/pagination-request.model';
import { SearchType } from '@shared/models/search-type.model';
import { HawbCreateDto } from '../models/hawb-create.model';

@Injectable({ providedIn: 'root' })
export class HawbSearchRequestService extends ApiService implements AbstractAutocompleteService<CodeName> {
	constructor(http: HttpClient) {
		super(http);
	}

	getHawbList(pageParams: PaginationRequest, hawbSearchPayload: HawbSearchPayload): Observable<PaginationResponse<HawbListObject>> {
		return super.getData<PaginationResponse<HawbListObject>>('hawb-management', {
			...pageParams,
			...hawbSearchPayload,
		});
	}

	getHawbDetail(hawbId: string) {
		return super.getData<HawbCreateDto>(`hawb-management/info`, {
			hawbId,
		});
	}

	paramQuery(keyword: string, queryKey: string): Observable<CodeName[]> {
		return super.getData<Record<string, string[]>>('hawb-management/param-query').pipe(
			map((res) => {
				return res[queryKey]
					.filter((item: string) => item.toLowerCase().includes(keyword.toLowerCase().trim()))
					.map((item: string) => {
						return { code: item, name: item };
					});
			})
		);
	}

	createHawb(sliNumber: string, data: Omit<HawbCreateDto, 'sliId'>) {
		return super.postData<any>('hawb-management', { ...data, sliId: sliNumber });
	}

	updateHawb(hawbId: string, sliId: string, orgId: string, data: HawbCreateDto) {
		return super.updateDataPatch<any>(`hawb-management`, { ...data, sliId, orgId, id: hawbId });
	}

	getOptions(keyword: string, id?: string): Observable<CodeName[]> {
		switch (id) {
			case SearchType.SHIPPER: {
				return this.paramQuery(keyword, 'shipperNameList');
			}

			case SearchType.CONSIGNEE: {
				return this.paramQuery(keyword, 'consigneeNameList');
			}

			case SearchType.HAWB: {
				return this.paramQuery(keyword, 'hawbNumberList');
			}

			case SearchType.AIRPORT: {
				return of(AIRPORTS).pipe(
					map((airports: Airport[]) => {
						return airports
							.filter((airport: Airport) => airport.code.toLowerCase().includes(keyword.toLowerCase().trim()))
							.map((airport: Airport) => {
								return { code: airport.code, name: airport.name };
							});
					})
				);
			}

			default:
				return of([]);
		}
	}
}
