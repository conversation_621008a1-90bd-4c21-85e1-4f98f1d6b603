import { ChangeDetectionStrategy, Component, Output, EventEmitter, ViewChildren, QueryList } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { provideNativeDateAdapter } from '@angular/material/core';
import { FormGroup, FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { DestroyRefComponent } from '@shared/components/destroy-observable/destroy-ref.component';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { DatePipe, CommonModule } from '@angular/common';
import { AutocompleteComponent } from '@shared/components/autocomplete/autocomplete.component';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { CodeName } from '@shared/models/code-name.model';
import { HawbSearchPayload } from '../../models/hawb-search-payload.model';
import { SearchType } from '@shared/models/search-type.model';

const DATE_FORMAT = 'yyyy-MM-dd';

@Component({
	selector: 'orll-hawb-search',
	templateUrl: './hawb-search.component.html',
	styleUrls: ['./hawb-search.component.scss'],
	changeDetection: ChangeDetectionStrategy.OnPush,
	providers: [provideNativeDateAdapter(), DatePipe],
	imports: [
		MatInputModule,
		MatIconModule,
		MatChipsModule,
		MatButtonModule,
		MatDatepickerModule,
		TranslateModule,
		ReactiveFormsModule,
		MatAutocompleteModule,
		CommonModule,
		AutocompleteComponent,
	],
})
export class HawbSearchComponent extends DestroyRefComponent {
	@Output() searchHawb = new EventEmitter<HawbSearchPayload>();
	@ViewChildren('shipperAutocomplete,consigneeAutocomplete,departureAutocomplete,arrivalAutocomplete,hawbNumberAutocomplete')
	autocompleteList!: QueryList<AutocompleteComponent<any>>;

	hawbSearchForm: FormGroup = new FormGroup({
		goodsDescription: new FormControl<string>(''),
		startDate: new FormControl<Date | null>(null),
		endDate: new FormControl<Date | null>(null),
	});

	selectedShippers: CodeName[] = [];
	selectedConsignees: CodeName[] = [];
	selectedDepartureLocations: CodeName[] = [];
	selectedArrivalLocations: CodeName[] = [];
	selectedHawbNumbers: CodeName[] = [];

	constructor(
		public readonly hawbSearchRequestService: HawbSearchRequestService,
		private readonly datePipe: DatePipe
	) {
		super();
	}

	// eslint-disable-next-line
	selectedItems(item: CodeName[], keyword: string): void {
		switch (keyword) {
			case SearchType.SHIPPER: {
				this.selectedShippers = item;
				break;
			}

			case SearchType.CONSIGNEE: {
				this.selectedConsignees = item;
				break;
			}

			case SearchType.DEPARTURE: {
				this.selectedDepartureLocations = item;
				break;
			}

			case SearchType.ARRIVAL: {
				this.selectedArrivalLocations = item;
				break;
			}

			case SearchType.HAWB: {
				this.selectedHawbNumbers = item;
				break;
			}

			default:
				break;
		}
	}

	onSearch(): void {
		const payload: HawbSearchPayload = {
			goodsDescription: this.hawbSearchForm.value.goodsDescription ?? '',
			createDateStart: this.datePipe.transform(this.hawbSearchForm.value.startDate, DATE_FORMAT) ?? null,
			createDateEnd: this.datePipe.transform(this.hawbSearchForm.value.endDate, DATE_FORMAT) ?? null,
			shipperNameList: this.selectedShippers.map((shipper) => shipper.name),
			consigneeNameList: this.selectedConsignees.map((consignee) => consignee.name),
			departureLocationList: this.selectedDepartureLocations.map((location) => location.code),
			arrivalLocationList: this.selectedArrivalLocations.map((location) => location.code),
			hawbNumberList: this.selectedHawbNumbers.map((hawbNumber) => hawbNumber.code),
		};
		this.searchHawb.emit(payload);
	}

	onReset(event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		this.hawbSearchForm.reset();
		this.selectedShippers = [];
		this.selectedConsignees = [];
		this.selectedDepartureLocations = [];
		this.selectedArrivalLocations = [];
		this.selectedHawbNumbers = [];
		this.autocompleteList.forEach((autocomplete) => autocomplete.eraseValue(event));
	}
}
