import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ShipperOrConsigneeInfoComponent } from './shipper-or-consignee-info.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';

describe('ShipperOrConsigneeInfoComponent', () => {
	let component: ShipperOrConsigneeInfoComponent;
	let fixture: ComponentFixture<ShipperOrConsigneeInfoComponent>;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [ShipperOrConsigneeInfoComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(ShipperOrConsigneeInfoComponent);
		component = fixture.componentInstance;
		fixture.detectChanges();
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});
});
