import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { Location } from '@angular/common';
import { ShipperOrConsigneeInfoComponent } from './shipper-or-consignee-info/shipper-or-consignee-info.component';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CarrierAgentComponent } from './carrier-agent/carrier-agent.component';
import { IssuedByComponent } from './issued-by/issued-by.component';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { FormControl, FormGroup, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { AirportInfoComponent } from './airport-info/airport-info.component';
import { MatTableModule } from '@angular/material/table';
import { OtherChangesComponent } from './other-changes/other-changes.component';
import { SliConsigneeComponent } from '../../../sli-mgmt/components/sli-consignee/sli-consignee.component';
import { ShipmentParty } from '../../../sli-mgmt/models/shipment-party.model';
import { PrepaidCollectComponent } from './prepaid-collect/prepaid-collect.component';
import { combineLatest, startWith, tap } from 'rxjs';
import { ShipmentPartyCompanyType } from '../../models/hawb-sli-info.model';
import { SliCreateRequestService } from '../../../sli-mgmt/services/sli-create-request.service';
import { SliCreatePayload } from '../../../sli-mgmt/models/sli-create-payload.model';
import { SelectOrgDialogComponent } from '@shared/components/select-org-dialog/select-org-dialog.component';
import { Person } from '@shared/models/person.model';
import { OrgType } from '@shared/models/org-type.model';
import { MatDialog } from '@angular/material/dialog';
import { HawbSearchRequestService } from '../../services/hawb-search-request.service';
import { NotificationService } from '@shared/services/notification.service';
import { HawbCreateDto, OtherChargeList, PartyList } from '../../models/hawb-create.model';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatOptionModule } from '@angular/material/core';
import { EnumCodeFormItemComponent } from '@shared/components/enum-code-form-item/enum-code-form-item.component';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';
import { AuthService } from '@shared/auth/auth.service';
import { OrgMgmtRequestService } from '@shared/services/org-mgmt-request.service';
import { Router } from '@angular/router';
import { isBlank } from '@shared/utils/type.utils';

@Component({
	selector: 'orll-create-hawb-from-shared-sli',
	imports: [
		ShipperOrConsigneeInfoComponent,
		MatButtonModule,
		MatExpansionModule,
		MatIconModule,
		TranslateModule,
		SliConsigneeComponent,
		CarrierAgentComponent,
		IssuedByComponent,
		MatFormFieldModule,
		MatInput,
		ReactiveFormsModule,
		AirportInfoComponent,
		MatTableModule,
		OtherChangesComponent,
		PrepaidCollectComponent,
		CurrencyInputComponent,
		MatAutocompleteModule,
		MatOptionModule,
		EnumCodeFormItemComponent,
	],
	templateUrl: './create-hawb-from-shared-sli.component.html',
	styleUrl: './create-hawb-from-shared-sli.component.scss',
})
export default class CreateHawbFromSharedSliComponent implements OnInit {
	protected readonly enumCodeTypeModel = EnumCodeTypeModel;

	@Input() sliNumber?: string;

	@Input() hawbId?: string;

	@ViewChild(CarrierAgentComponent)
	carrierAgentComponent!: CarrierAgentComponent;

	@ViewChild(AirportInfoComponent)
	airPortInfoComponent!: AirportInfoComponent;

	@ViewChild(OtherChangesComponent)
	otherChangesComponent!: OtherChangesComponent;

	private sliId: string | undefined;
	private orgId: string | undefined;

	alsoNotifies: ShipmentParty[] = [];

	hawbForm = this.fb.group({
		hawbPrefix: ['', [Validators.required]],
		hawbNumber: ['', [Validators.required]],
		accountingInformation: [null],
		handingInformation: [null],
		noOfPiecesRcp: [''],
		grossWeight: [null, [Validators.required, Validators.pattern(/^\d+(\.\d{1})?$/)]],
		rateClass: [''],
		chargeableWeight: [null, [Validators.required, Validators.pattern(/^\d+(\.\d{1}})?$/)]],
		rateCharge: new FormGroup(
			{
				currencyUnit: new FormControl(''),
				numericalValue: new FormControl<number | null>(null),
			},
			[Validators.required]
		),
		total: new FormControl<string | null>(null, Validators.pattern(/^\d+(\.\d+)?$/)),
		natureAndQuantityOfGoods: ['', [Validators.required]],
		//last row
		date: ['', [Validators.required, Validators.pattern(/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/)]],
		atPlace: ['', [Validators.required]],
		signatureOfShipperOrHisAgent: ['', [Validators.required]],
		signatureOfCarrierOrItsAgent: ['', [Validators.required]],
	});

	displayedColumns: string[] = [
		'noOfPiecesRcp',
		'grossWeight',
		'rateClass',
		'chargeableWeight',
		'rateCharge',
		'total',
		'natureAndQuantityOfGoods',
	];
	dataSource = [{ position: 1, name: 'Hydrogen', weight: 1.0079, symbol: 'H' }];

	public shipperInfo: ShipmentParty = {} as ShipmentParty;
	public consigneeInfo: ShipmentParty = {} as ShipmentParty;
	public currencies: string[] = [];

	constructor(
		private readonly fb: NonNullableFormBuilder,
		private readonly sliCreateRequestService: SliCreateRequestService,
		private readonly orgMgmtRequestService: OrgMgmtRequestService,
		private readonly hawbSearchRequestService: HawbSearchRequestService,
		private readonly notificationService: NotificationService,
		private readonly translateService: TranslateService,
		private readonly dialog: MatDialog,
		private readonly location: Location,
		private readonly authService: AuthService,
		private readonly router: Router
	) {}

	ngOnInit(): void {
		this.sliCreateRequestService.getCurrencies().subscribe((currencies: string[]) => {
			this.currencies = ['', ...currencies];
		});

		combineLatest([
			this.hawbForm.get('chargeableWeight')!.valueChanges.pipe(startWith(this.hawbForm.get('chargeableWeight')?.value)),
			this.hawbForm
				.get('rateCharge')!
				.get('numericalValue')!
				.valueChanges.pipe(startWith(this.hawbForm.get('rateCharge')!.get('numericalValue')?.value)),
		])
			.pipe(
				tap(([chargeableWeight, rateChargeNumber]) => {
					if (chargeableWeight && rateChargeNumber !== null && rateChargeNumber !== undefined && +rateChargeNumber >= 0) {
						this.hawbForm.patchValue({
							total: `${+chargeableWeight * rateChargeNumber}`,
						});
					}
				})
			)
			.subscribe();

		if (this.sliNumber) {
			this.sliCreateRequestService
				.getSliDetail(this.sliNumber)
				.pipe(
					tap((result: SliCreatePayload) => {
						if (result) {
							const { shipmentParty } = result;
							const shipperInfo =
								shipmentParty.find((party) => party.companyType === ShipmentPartyCompanyType.SHIPPER) ||
								({} as ShipmentParty);
							const consigneeInfo =
								shipmentParty.find((party) => party.companyType === ShipmentPartyCompanyType.CONSIGNEE) ||
								({} as ShipmentParty);

							this.shipperInfo = shipperInfo;
							this.consigneeInfo = consigneeInfo;
						}
					})
				)
				.subscribe();
			const payload = this.authService.getPayload();
			if (payload?.sub) {
				const orgId = payload.sub.split('|')[1];
				if (orgId) {
					this.orgMgmtRequestService.getOrgInfo(orgId).subscribe((result) => {
						const personInfo = result.persons?.[0];

						this.carrierAgentComponent.carrierAgentForm.patchValue({
							company: result.companyName,
							agentIataCode: result.iataCargoAgentCode,
							country: result.countryCode,
							province: result.regionCode,
							cityName: result.cityCode,
							textualPostCode: result.textualPostCode,
							address: result.locationName,
							phoneNumber: personInfo?.phoneNumber,
							email: personInfo?.emailAddress,
						});
					});
				}
			}
		}
		if (this.hawbId) {
			this.hawbSearchRequestService
				.getHawbDetail(this.hawbId)
				.pipe(
					tap((result: HawbCreateDto) => {
						if (result) {
							const { sliPartyList, partyList, sliId, orgId } = result;
							this.sliId = sliId;
							this.orgId = orgId;
							if (sliPartyList && sliPartyList.length > 0) {
								this.shipperInfo =
									sliPartyList.find((party) => party.companyType === ShipmentPartyCompanyType.SHIPPER) ||
									({} as ShipmentParty);
								this.consigneeInfo =
									sliPartyList.find((party) => party.companyType === ShipmentPartyCompanyType.CONSIGNEE) ||
									({} as ShipmentParty);
							}
							if (partyList && partyList.length > 0) {
								this.alsoNotifies = partyList.filter((it) => !it.companyType);
							}
							const carrierAgent = partyList.find((party) => party.companyType === ShipmentPartyCompanyType.CARRIER);
							if (carrierAgent) {
								this.carrierAgentComponent.carrierAgentForm.patchValue(
									{
										company: carrierAgent.companyName,
										agentIataCode: carrierAgent.iataCargoAgentCode,
										country: carrierAgent.countryCode,
										province: carrierAgent.regionCode,
										cityName: carrierAgent.cityCode,
										textualPostCode: carrierAgent.textualPostCode,
										address: carrierAgent.locationName,
										phoneNumber: carrierAgent.phoneNumber,
										email: carrierAgent.emailAddress,
									},
									{
										emitEvent: false,
									}
								);
							}

							const {
								waybillPrefix,
								waybillNumber,
								accountingInformation,
								textualHandlingInstructions,
								totalGrossWeight,
								rateClassCode,
								totalVolumetricWeight,
								rateCharge,
								goodsDescriptionForRate,
								carrierDeclarationDate,
								carrierDeclarationPlace,
								consignorDeclarationSignature,
								carrierDeclarationSignature,
							} = result;
							this.hawbForm.patchValue({
								hawbPrefix: waybillPrefix,
								hawbNumber: waybillNumber,
								accountingInformation: accountingInformation as any,
								handingInformation: textualHandlingInstructions as any,
								// noOfPiecesRcp: null,
								grossWeight: totalGrossWeight as any,
								rateClass: rateClassCode,
								chargeableWeight: totalVolumetricWeight as any,
								rateCharge: {
									currencyUnit: rateCharge.currencyUnit,
									numericalValue: rateCharge.numericalValue,
								},
								natureAndQuantityOfGoods: goodsDescriptionForRate,
								date: carrierDeclarationDate,
								atPlace: carrierDeclarationPlace,
								signatureOfShipperOrHisAgent: consignorDeclarationSignature,
								signatureOfCarrierOrItsAgent: carrierDeclarationSignature,
							});

							this.airPortInfoComponent.airportInfoForm.patchValue({
								departureAndRequestedRouting: result.departureLocation,
								airportOfDestination: result.arrivalLocation,
								amountOfInsurance: {
									currencyUnit: result.insuredAmount.currencyUnit,
									numericalValue: result.insuredAmount.numericalValue,
								},
								// following data will come from mawb
								// flight
								// to
								// toBy2ndCarrier
								// toBy3rdCarrier
								// date
								// byFirstCarrier
								// by2ndCarrier
								// by3rdCarrier
								wtOrVal: result.weightValuationIndicator,
								other: result.otherChargesIndicator,
								declaredValueForCarriage: {
									currencyUnit: result.declaredValueForCarriage.currencyUnit,
									numericalValue: result.declaredValueForCarriage.numericalValue,
								},
								declaredValueForCustoms: {
									currencyUnit: result.declaredValueForCustoms.currencyUnit,
									numericalValue: result.declaredValueForCustoms.numericalValue,
								},
							});

							const { otherChargeList } = result;
							if (otherChargeList && otherChargeList.length > 0) {
								this.otherChangesComponent.otherChangesList = otherChargeList.map((it) => {
									return {
										chargePaymentType: it.chargePaymentType,
										entitlement: it.entitlement,
										otherChargeCode: it.otherChargeCode,
										otherChargeAmount: {
											currencyUnit: it.otherChargeAmount?.currencyUnit ?? '',
											numericalValue: it.otherChargeAmount?.numericalValue ?? null,
										},
									};
								});
							}
						}
					})
				)
				.subscribe();
		}
	}

	delAlsoNotify(index: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		this.alsoNotifies.splice(index, 1);
	}

	addAlsoNotify(): void {
		const newNotify = {
			companyName: '',
			contactName: '',
			countryCode: '',
			regionCode: '',
			cityCode: '',
			textualPostCode: '',
			locationName: '',
			phoneNumber: '',
			emailAddress: '',
			companyType: '',
		};
		this.alsoNotifies.push(newNotify);
	}

	getOrgList(idx: number, event: Event): void {
		event.preventDefault();
		event.stopPropagation();
		const dialogRef = this.dialog.open(SelectOrgDialogComponent, {
			width: '400px',
			data: {
				orgType: '',
			},
		});

		dialogRef.afterClosed().subscribe((result) => {
			if (!result) return;

			this.alsoNotifies = this.alsoNotifies.map((item, index) => {
				if (idx === index) {
					return {
						...item,
						companyName: result.companyName,
						contactName:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.contactName ?? '',
						countryCode: result.countryCode,
						regionCode: result.regionCode,
						cityCode: result.cityCode,
						textualPostCode: result.textualPostCode,
						locationName: result.locationName,
						phoneNumber:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.phoneNumber ?? '',
						emailAddress:
							result.persons.find((person: Person) => person.contactRole === OrgType.CUSTOMER_CONTACT)?.emailAddress ?? '',
						companyType: '',
					};
				}
				return item;
			});
		});
	}

	onCancel() {
		this.location.back();
	}

	onSave() {
		this.hawbForm.markAllAsTouched();
		this.airPortInfoComponent.airportInfoForm.markAllAsTouched();
		this.carrierAgentComponent.carrierAgentForm.markAllAsTouched();
		if (
			this.hawbForm.invalid ||
			this.carrierAgentComponent.carrierAgentForm.invalid ||
			this.airPortInfoComponent.airportInfoForm.invalid
		) {
			this.notificationService.showError(this.translateService.instant('hawb.formInvalid'));
			return;
		}

		const {
			hawbPrefix,
			hawbNumber,
			accountingInformation,
			handingInformation,
			// noOfPiecesRcp,
			grossWeight,
			rateClass,
			chargeableWeight,
			rateCharge,
			// total,
			natureAndQuantityOfGoods,
			date,
			atPlace,
			signatureOfShipperOrHisAgent,
			signatureOfCarrierOrItsAgent,
		} = this.hawbForm.value;

		const airPortInfo = this.airPortInfoComponent.airportInfoForm.value;

		const otherCharges: OtherChargeList[] = this.otherChangesComponent.otherChangesList.map((it) => {
			return {
				chargePaymentType: it.chargePaymentType,
				entitlement: it.entitlement,
				otherChargeCode: it.otherChargeCode,
				otherChargeAmount: it.otherChargeAmount,
			};
		});

		const alsoNotifies: PartyList[] = this.alsoNotifies.map((item) => {
			return {
				companyName: item.companyName,
				contactName: item.contactName,
				countryCode: item.countryCode,
				regionCode: item.regionCode,
				locationName: item.locationName,
				cityCode: item.cityCode,
				textualPostCode: item.textualPostCode,
				phoneNumber: item.phoneNumber,
				emailAddress: item.emailAddress,
				companyType: item.companyType,
				iataCargoAgentCode: '',
			};
		});

		this.carrierAgentComponent.carrierAgentForm.markAllAsTouched();
		if (this.carrierAgentComponent.carrierAgentForm.invalid) {
			this.notificationService.showError(this.translateService.instant('hawb.carrierAgent.formInvalid'));
			return;
		}

		const carrierAgentValue = this.carrierAgentComponent.carrierAgentForm.value;

		const saveData = {
			orgId: '',
			// waybillType: '',
			waybillPrefix: hawbPrefix!,
			waybillNumber: hawbNumber!,
			partyList: [
				...alsoNotifies,
				{
					companyName: carrierAgentValue.company ?? '',
					companyType: 'FFW',
					countryCode: carrierAgentValue.country ?? '',
					contactName: '',
					regionCode: carrierAgentValue.province ?? '',
					locationName: carrierAgentValue.address ?? '',
					iataCargoAgentCode: carrierAgentValue.agentIataCode ?? '',
					cityCode: carrierAgentValue.cityName ?? '',
					textualPostCode: carrierAgentValue.textualPostCode ?? '',
					phoneNumber: carrierAgentValue.phoneNumber ?? '',
					emailAddress: carrierAgentValue.email ?? '',
				},
			],
			accountingInformation: accountingInformation!,
			departureLocation: airPortInfo.departureAndRequestedRouting ?? '',
			arrivalLocation: airPortInfo.airportOfDestination ?? '',
			insuredAmount: {
				currencyUnit: airPortInfo.amountOfInsurance?.currencyUnit ?? '',
				numericalValue: airPortInfo.amountOfInsurance?.numericalValue ?? null,
			},
			weightValuationIndicator: airPortInfo.wtOrVal ?? '',
			otherChargesIndicator: airPortInfo.other ?? '',
			declaredValueForCarriage: {
				currencyUnit: airPortInfo.declaredValueForCarriage?.currencyUnit ?? '',
				numericalValue: airPortInfo.declaredValueForCarriage?.numericalValue ?? null,
			},
			declaredValueForCustoms: {
				currencyUnit: airPortInfo.declaredValueForCustoms?.currencyUnit ?? '',
				numericalValue: airPortInfo.declaredValueForCustoms?.numericalValue ?? null,
			},
			textualHandlingInstructions: handingInformation ?? '',
			totalGrossWeight: !isBlank(grossWeight) ? Number(grossWeight) : null,
			rateClassCode: rateClass ?? '',
			totalVolumetricWeight: +(chargeableWeight ?? 0),
			rateCharge: {
				currencyUnit: rateCharge?.currencyUnit ?? '',
				numericalValue: rateCharge?.numericalValue ?? null,
			},
			goodsDescriptionForRate: natureAndQuantityOfGoods ?? '',
			otherChargeList: otherCharges,
			carrierDeclarationDate: date!,
			carrierDeclarationPlace: atPlace!,
			consignorDeclarationSignature: signatureOfShipperOrHisAgent!,
			carrierDeclarationSignature: signatureOfCarrierOrItsAgent!,
		};
		if (this.sliNumber && !this.hawbId) {
			this.hawbSearchRequestService
				.createHawb(this.sliNumber, saveData)
				.pipe(
					tap(() => {
						this.notificationService.showSuccess(this.translateService.instant('hawb.createHawb.success'));
						this.router.navigate(['/hawb']);
					})
				)
				.subscribe();
		}

		if (this.hawbId && this.sliId && this.orgId) {
			this.hawbSearchRequestService
				.updateHawb(this.hawbId, this.sliId, this.orgId, saveData)
				.pipe(
					tap(() => {
						this.notificationService.showSuccess(this.translateService.instant('hawb.updateHawb.success'));
						this.router.navigate(['/hawb']);
					})
				)
				.subscribe();
		}
	}
}
