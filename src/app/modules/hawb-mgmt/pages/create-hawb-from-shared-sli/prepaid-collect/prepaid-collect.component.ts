import { Component, DestroyRef, OnInit } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { CommonModule } from '@angular/common';
import { FormControl, NonNullableFormBuilder, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { combineLatest, startWith } from 'rxjs';
import { isBlank } from '@shared/utils/type.utils';

@Component({
	selector: 'orll-prepaid-collect',
	imports: [CommonModule, ReactiveFormsModule, TranslatePipe, MatFormFieldModule, MatInput],
	templateUrl: './prepaid-collect.component.html',
	styleUrl: './prepaid-collect.component.scss',
})
export class PrepaidCollectComponent implements OnInit {
	prepaidForm = this.fb.group({
		weightChargePrepaid: new FormControl<number | null>(null),
		weightChargeCollect: new FormControl<number | null>(null),
		valuationChargePrepaid: new FormControl<number | null>(null),
		valuationChargeCollect: new FormControl<number | null>(null),
		taxPrepaid: new FormControl<number | null>(null),
		taxCollect: new FormControl<number | null>(null),
		totalOtherChargesDueAgentPrepaid: new FormControl<number | null>(null),
		totalOtherChargesDueAgentCollect: new FormControl<number | null>(null),
		totalOtherChargesDueCarrierPrepaid: new FormControl<number | null>(null),
		totalOtherChargesDueCarrierCollect: new FormControl<number | null>(null),
		totalPrepaid: new FormControl<number | null>(null),
		totalCollect: new FormControl<number | null>(null),
	});

	constructor(
		private readonly fb: NonNullableFormBuilder,
		private readonly destroyRef: DestroyRef,
	) {
	}

	ngOnInit(): void {
		combineLatest([
			this.prepaidForm.get('weightChargePrepaid')!.valueChanges.pipe(startWith(this.prepaidForm.get('weightChargePrepaid')?.value)),
			this.prepaidForm
				.get('valuationChargePrepaid')!
				.valueChanges.pipe(startWith(this.prepaidForm.get('valuationChargePrepaid')?.value)),
		])
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe(([weightCharge, valuationCharge]) => {
				if (!isBlank(weightCharge) && !isBlank(valuationCharge)) {
					this.prepaidForm.patchValue({
						totalPrepaid: Number(weightCharge) + Number(valuationCharge),
					});
				} else {
					this.prepaidForm.patchValue({
						totalPrepaid: null,
					});
				}
			});

		combineLatest([
			this.prepaidForm
				.get('weightChargeCollect')!
				.valueChanges.pipe(startWith(this.prepaidForm.get('weightChargeCollect')?.value ?? 0)),
			this.prepaidForm
				.get('valuationChargeCollect')!
				.valueChanges.pipe(startWith(this.prepaidForm.get('valuationChargeCollect')?.value ?? 0)),
		])
			.pipe(takeUntilDestroyed(this.destroyRef))
			.subscribe(([weightCharge, valuationCharge]) => {
				if (!isBlank(weightCharge) && !isBlank(valuationCharge)) {
					this.prepaidForm.patchValue({
						totalCollect: Number(weightCharge) + Number(valuationCharge),
					});
				} else {
					this.prepaidForm.patchValue({
						totalCollect: null,
					});
				}
			});
	}
}
