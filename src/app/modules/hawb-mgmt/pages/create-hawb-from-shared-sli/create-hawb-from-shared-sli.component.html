<div class="orll-sli-also-notify">
	<form [formGroup]="hawbForm">
		<div class="row gutters-x-3">
			<div class="col-2">
				<mat-form-field class="width-full">
					<mat-label>{{ 'hawb.formItem.hawbPrefix' | translate }}</mat-label>
					<input matInput formControlName="hawbPrefix" />
					@if (hawbForm.get('hawbPrefix')?.hasError('required')) {
						<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.hawbPrefix' | translate } }}</mat-error>
					}
				</mat-form-field>
			</div>
			<div class="col-2">
				<mat-form-field class="width-full">
					<mat-label>{{ 'hawb.formItem.hawbNumber' | translate }}</mat-label>
					<input matInput formControlName="hawbNumber" />
					@if (hawbForm.get('hawbNumber')?.hasError('required')) {
						<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.hawbNumber' | translate } }}</mat-error>
					}
				</mat-form-field>
			</div>
		</div>

		<div class="row gutters-x-3">
			<div class="col-6">
				<div class="iata-box">
					<orll-shipper-or-consignee-info title="shipper"
						[shipmentParty]="shipperInfo"></orll-shipper-or-consignee-info>
				</div>
			</div>
			<div class="col-6">
				<div class="iata-box">
					<orll-shipper-or-consignee-info title="consignee"
						[shipmentParty]="consigneeInfo"></orll-shipper-or-consignee-info>
				</div>
			</div>
		</div>

		<div class="row margin-r-5">
			<div class="col-12">
				<div class="iata-box orll-sli-also-notify__box">
					@for (alsoNotify of alsoNotifies; track $index) {
						<mat-expansion-panel class="orll-sli-also-notify__panel" [expanded]="true">
							<mat-expansion-panel-header>
								<mat-panel-title>
									<h2 class="mat-display-2 orll-sli-also-notify__title">
										{{ 'sli.mgmt.company.alsoNotify' | translate }}
									</h2>
								</mat-panel-title>
								<mat-panel-description>
									<button mat-icon-button color="primary" (click)="delAlsoNotify($index, $event)"
										class="orll-sli-also-notify__delete-button">
										<mat-icon>delete</mat-icon>
									</button>
									<button mat-icon-button color="primary" (click)="getOrgList($index, $event)"
										class="orll-sli-also-notify__contact-button">
										<mat-icon>contacts</mat-icon>
									</button>
								</mat-panel-description>
							</mat-expansion-panel-header>
							<orll-sli-consignee #sliAlsoNotify [shipmentParty]="alsoNotify"></orll-sli-consignee>
						</mat-expansion-panel>
					}

					<div class="orll-sli-also-notify__footer">
						<button mat-stroked-button type="button" color="primary" (click)="addAlsoNotify()"
							class="orll-sli-also-notify__add-button">
							<mat-icon>add</mat-icon>
							{{ 'sli.mgmt.company.alsoNotify' | translate }}
						</button>
					</div>
				</div>
			</div>
		</div>

		<div class="row gutters-x-3">
			<div class="col-6">
				<div class="iata-box">
					<orll-carrier-agent></orll-carrier-agent>
				</div>
			</div>
			<div class="col-6">
				<div class="iata-box">
					<orll-issued-by></orll-issued-by>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-12">
				<div class="iata-box">
					<div class="row">
						<mat-form-field appearance="outline" class="col-12" floatLabel="always">
							<mat-label>{{ 'hawb.formItem.accountingInformation' | translate }}</mat-label>
							<textarea matInput formControlName="accountingInformation"></textarea>
							@if (hawbForm.get('accountingInformation')?.hasError('required')) {
								<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.accountingInformation' | translate } }}</mat-error>
							}
						</mat-form-field>
					</div>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-12">
				<div class="iata-box">
					<orll-airport-info [currencies]="currencies"></orll-airport-info>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-12">
				<div class="iata-box">
					<div class="row">
						<mat-form-field appearance="outline" class="col-12" floatLabel="always">
							<mat-label>{{ 'hawb.formItem.handingInformation' | translate }}</mat-label>
							<textarea matInput formControlName="handingInformation"></textarea>
							@if (hawbForm.get('handingInformation')?.hasError('required')) {
								<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.handingInformation' | translate } }}</mat-error>
							}
						</mat-form-field>
					</div>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col-12">
				<div class="iata-box">
					<div class="piece-rcp-container">
						<div class="row gutters-x-3 no-label-form-header">
							<div class="col">{{ 'hawb.formItem.noOfPiecesRcp'|translate }}</div>
							<div class="col">{{ 'hawb.formItem.grossWeight'|translate }}</div>
							<div class="col">{{ 'hawb.formItem.rateClass'|translate }}</div>
							<div class="col">{{ 'hawb.formItem.chargeableWeight'|translate }}</div>
							<div class="col">{{ 'hawb.formItem.rateCharge'|translate }}</div>
							<div class="col">{{ 'hawb.formItem.total'|translate }}</div>
							<div
								class="col col-nature-and-quantity-of-goods">{{ 'hawb.formItem.natureAndQuantityOfGoods'|translate }}
							</div>
						</div>
						<div class="row gutters-x-3 no-label-form">
							<div class="col">
								<div>
									<mat-form-field readonly="true" subscriptSizing="dynamic" appearance="outline"
										floatLabel="always">
										<input matInput formControlName="noOfPiecesRcp" />
									</mat-form-field>
								</div>
							</div>
							<div class="col">
								<mat-form-field subscriptSizing="dynamic" appearance="outline" floatLabel="always">
									<input matInput formControlName="grossWeight" />
									<span matSuffix class="unit">KG</span>
									@if (hawbForm.get('grossWeight')?.hasError('required')) {
										<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.grossWeight' | translate } }}</mat-error>
									}
									@if (hawbForm.get('grossWeight')?.hasError('pattern')) {
										<mat-error>{{
												'validators.maxOneDecimal'|translate:{
													field: 'hawb.formItem.grossWeight' | translate
												}
											}}
										</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="col">

								<mat-form-field subscriptSizing="dynamic" appearance="outline" class="width-100"
									floatLabel="always">
									<orll-enum-code-form-item [enumType]="enumCodeTypeModel.RATE_CLASS_CODE"
										formControlName="rateClass"></orll-enum-code-form-item>
								</mat-form-field>
							</div>
							<div class="col">
								<mat-form-field subscriptSizing="dynamic" appearance="outline" floatLabel="always">
									<input matInput formControlName="chargeableWeight" />
									<span matSuffix class="unit">KG</span>
									@if (hawbForm.get('chargeableWeight')?.hasError('required')) {
										<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.chargeableWeight' | translate } }}</mat-error>
									}
									@if (hawbForm.get('chargeableWeight')?.hasError('pattern')) {
										<mat-error>{{
												'validators.maxOneDecimal'|translate:{
													field: 'hawb.formItem.chargeableWeight' | translate
												}
											}}
										</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="col">
								<orll-currency-input
									[formLabel]="null"
									[currencies]="currencies"
									[currencyForm]="hawbForm.controls.rateCharge"
								></orll-currency-input>
							</div>
							<div class="col">
								<mat-form-field readonly="true" subscriptSizing="dynamic" appearance="outline"
									floatLabel="always">
									<input matInput formControlName="total" />
									@if (hawbForm.get('total')?.hasError('pattern')) {
										<mat-error>{{
												'validators.maxOneDecimal'|translate:{
													field: 'hawb.formItem.total' | translate
												}
											}}
										</mat-error>
									}
								</mat-form-field>
							</div>
							<div class="col col-nature-and-quantity-of-goods">
								<mat-form-field subscriptSizing="dynamic" appearance="outline" floatLabel="always">
									<input matInput formControlName="natureAndQuantityOfGoods" />
									@if (hawbForm.get('natureAndQuantityOfGoods')?.hasError('required')) {
										<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.natureAndQuantityOfGoods' | translate } }}</mat-error>
									}
								</mat-form-field>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="row gutters-x-3">
			<div class="col-5">
				<div class="iata-box">
					<orll-prepaid-collect></orll-prepaid-collect>
				</div>
			</div>
			<div class="col-7">
				<div class="iata-box">
					<orll-other-changes [currencies]="currencies"></orll-other-changes>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="ml-auto col-8 align-self-end">
				<div class="row gutters-x-3">
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.formItem.date' | translate }}</mat-label>
						<input matInput formControlName="date" />
						@if (hawbForm.get('date')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.date' | translate } }}</mat-error>
						}
						@if (hawbForm.get('date')?.hasError('pattern')) {
							<mat-error>{{
									'validators.pattern'|translate:{
										requiredPattern: 'yyyy-MM-dd HH:mm:ss',
										field: 'hawb.formItem.date' | translate
									}
								}}
							</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.formItem.atPlace' | translate }}</mat-label>
						<input matInput formControlName="atPlace" />
						@if (hawbForm.get('atPlace')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.atPlace' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.formItem.signatureOfShipperOrHisAgent' | translate }}</mat-label>
						<input matInput formControlName="signatureOfShipperOrHisAgent" />
						@if (hawbForm.get('signatureOfShipperOrHisAgent')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.signatureOfShipperOrHisAgent' | translate } }}</mat-error>
						}
					</mat-form-field>
					<mat-form-field appearance="outline" class="col-3" floatLabel="always">
						<mat-label>{{ 'hawb.formItem.signatureOfCarrierOrItsAgent' | translate }}</mat-label>
						<input matInput formControlName="signatureOfCarrierOrItsAgent" />
						@if (hawbForm.get('signatureOfCarrierOrItsAgent')?.hasError('required')) {
							<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.formItem.signatureOfCarrierOrItsAgent' | translate } }}</mat-error>
						}
					</mat-form-field>
				</div>

			</div>
		</div>
	</form>

	<div class="d-flex">
		<div class="ml-auto align-self-end">
			<button mat-button class="cancel-button" (click)="onCancel()">{{ 'sli.mgmt.cancel' | translate }}</button>
			<button mat-stroked-button class="preview-button" color="primary">
				<mat-icon>
					<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path
							d="M2 18C1.45 18 0.979167 17.8042 0.5875 17.4125C0.195833 17.0208 0 16.55 0 16V2C0 1.45 0.195833 0.979167 0.5875 0.5875C0.979167 0.195833 1.45 0 2 0H16C16.55 0 17.0208 0.195833 17.4125 0.5875C17.8042 0.979167 18 1.45 18 2V16C18 16.55 17.8042 17.0208 17.4125 17.4125C17.0208 17.8042 16.55 18 16 18H2ZM2 16H16V4H2V16ZM9 14C7.63333 14 6.4125 13.6292 5.3375 12.8875C4.2625 12.1458 3.48333 11.1833 3 10C3.48333 8.81667 4.2625 7.85417 5.3375 7.1125C6.4125 6.37083 7.63333 6 9 6C10.3667 6 11.5875 6.37083 12.6625 7.1125C13.7375 7.85417 14.5167 8.81667 15 10C14.5167 11.1833 13.7375 12.1458 12.6625 12.8875C11.5875 13.6292 10.3667 14 9 14ZM9 11.5C8.58333 11.5 8.22917 11.3542 7.9375 11.0625C7.64583 10.7708 7.5 10.4167 7.5 10C7.5 9.58333 7.64583 9.22917 7.9375 8.9375C8.22917 8.64583 8.58333 8.5 9 8.5C9.41667 8.5 9.77083 8.64583 10.0625 8.9375C10.3542 9.22917 10.5 9.58333 10.5 10C10.5 10.4167 10.3542 10.7708 10.0625 11.0625C9.77083 11.3542 9.41667 11.5 9 11.5ZM9 12.5C9.7 12.5 10.2917 12.2583 10.775 11.775C11.2583 11.2917 11.5 10.7 11.5 10C11.5 9.3 11.2583 8.70833 10.775 8.225C10.2917 7.74167 9.7 7.5 9 7.5C8.3 7.5 7.70833 7.74167 7.225 8.225C6.74167 8.70833 6.5 9.3 6.5 10C6.5 10.7 6.74167 11.2917 7.225 11.775C7.70833 12.2583 8.3 12.5 9 12.5Z"
							fill="#1E32FA" />
					</svg>
				</mat-icon>
				{{ 'hawb.preview.awb' | translate }}
			</button>
			<button mat-flat-button color="primary" (click)="onSave()">
				<mat-icon>save</mat-icon>
				{{ 'sli.mgmt.save' | translate }}
			</button>
		</div>
	</div>
</div>
