<h2 class="mat-display-2 orll-carrier-agent-form__title">{{ 'hawb.carrierAgent.title' | translate }}</h2>
<form [formGroup]="carrierAgentForm">
	<div class="row">
		<mat-form-field appearance="outline" class="col-6" floatLabel="always">
			<mat-label>{{ 'hawb.carrierAgent.company' | translate }}</mat-label>
			<input matInput formControlName="company">
			@if (carrierAgentForm.get('company')?.hasError('required')) {
				<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.company' | translate } }}</mat-error>
			}
		</mat-form-field>
		<mat-form-field appearance="outline" class="col-6" floatLabel="always">
			<mat-label>{{ 'hawb.carrierAgent.agentIataCode' | translate }}</mat-label>
			<input matInput formControlName="agentIataCode">
			@if (carrierAgentForm.get('agentIataCode')?.hasError('required')) {
				<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.agentIataCode' | translate } }}</mat-error>
			}
			@if (carrierAgentForm.get('agentIataCode')?.hasError('pattern')) {
				<mat-error>{{
						'validators.pattern'|translate:{
							field: 'hawb.carrierAgent.agentIataCode' | translate,
							requiredPattern: carrierAgentForm.get('agentIataCode')?.getError('pattern').requiredPattern
						}
					}}
				</mat-error>
			}
		</mat-form-field>
	</div>
	<div class="row">
		<mat-form-field appearance="outline" class="col-3" floatLabel="always">
			<mat-label>{{ 'hawb.carrierAgent.country' | translate }}</mat-label>
			<input matInput formControlName="country">
			@if (carrierAgentForm.get('country')?.hasError('required')) {
				<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.country' | translate } }}</mat-error>
			}
		</mat-form-field>
		<mat-form-field appearance="outline" class="col-3" floatLabel="always">
			<mat-label>{{ 'hawb.carrierAgent.province' | translate }}</mat-label>
			<input matInput formControlName="province">
			@if (carrierAgentForm.get('province')?.hasError('required')) {
				<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.province' | translate } }}</mat-error>
			}
		</mat-form-field>
		<mat-form-field appearance="outline" class="col-3" floatLabel="always">
			<mat-label>{{ 'hawb.carrierAgent.cityName' | translate }}</mat-label>
			<input matInput formControlName="cityName">
			@if (carrierAgentForm.get('cityName')?.hasError('required')) {
				<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.cityName' | translate } }}</mat-error>
			}
		</mat-form-field>
		<mat-form-field appearance="outline" class="col-3" floatLabel="always">
			<mat-label>{{ 'hawb.carrierAgent.textualPostCode' | translate }}</mat-label>
			<input matInput formControlName="textualPostCode">
			@if (carrierAgentForm.get('textualPostCode')?.hasError('required')) {
				<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.textualPostCode' | translate } }}</mat-error>
			}
		</mat-form-field>
	</div>
	<div class="row">
		<mat-form-field appearance="outline" class="col-6" floatLabel="always">
			<mat-label>{{ 'hawb.carrierAgent.address' | translate }}</mat-label>
			<input matInput formControlName="address">
			@if (carrierAgentForm.get('address')?.hasError('required')) {
				<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.address' | translate } }}</mat-error>
			}
		</mat-form-field>
		<mat-form-field appearance="outline" class="col-3" floatLabel="always">
			<mat-label>{{ 'hawb.carrierAgent.phoneNumber' | translate }}</mat-label>
			<input matInput formControlName="phoneNumber">
			@if (carrierAgentForm.get('phoneNumber')?.hasError('required')) {
				<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.phoneNumber' | translate } }}</mat-error>
			}
		</mat-form-field>
		<mat-form-field appearance="outline" class="col-3" floatLabel="always">
			<mat-label>{{ 'hawb.carrierAgent.email' | translate }}</mat-label>
			<input matInput formControlName="email">
			@if (carrierAgentForm.get('email')?.hasError('required')) {
				<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.carrierAgent.email' | translate } }}</mat-error>
			}
		</mat-form-field>
	</div>
</form>
