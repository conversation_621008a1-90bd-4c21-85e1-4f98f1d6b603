import { ComponentFixture, TestBed } from '@angular/core/testing';
import { OtherChangesComponent } from './other-changes.component';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { provideTranslateService } from '@ngx-translate/core';
import { Validators } from '@angular/forms';
import { NotificationService } from '@shared/services/notification.service';
import { By } from '@angular/platform-browser';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';

describe('OtherChangesComponent', () => {
	let component: OtherChangesComponent;
	let fixture: ComponentFixture<OtherChangesComponent>;
	let notificationService: any;
	const mockCurrencies = ['USD', 'EUR', 'GBP'];
	let notificationServiceShowError: any;

	beforeEach(async () => {
		await TestBed.configureTestingModule({
			imports: [OtherChangesComponent],
			providers: [provideHttpClient(withInterceptorsFromDi()), provideHttpClientTesting(), provideTranslateService()],
		}).compileComponents();

		fixture = TestBed.createComponent(OtherChangesComponent);
		component = fixture.componentInstance;

		component.currencies = mockCurrencies;
		fixture.detectChanges();

		notificationService = TestBed.inject(NotificationService);
		notificationServiceShowError = spyOn(notificationService, 'showError');
	});

	it('should create', () => {
		expect(component).toBeTruthy();
	});

	it('should initialize form with required validators', () => {
		const form = component.otherChangesForm;
		expect(form.get('chargePaymentType')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('entitlement')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('otherChargeCode')?.hasValidator(Validators.required)).toBeTruthy();
		expect(form.get('otherChargeAmount')?.hasValidator(Validators.required)).toBeTruthy();
	});

	it('should mark form as touched when adding invalid entry', () => {
		const markAllAsTouchedSpy = spyOn(component.otherChangesForm, 'markAllAsTouched');

		component.addOtherChanges();

		expect(markAllAsTouchedSpy).toHaveBeenCalled();
		expect(notificationServiceShowError).toHaveBeenCalledWith('hawb.otherChanges.formInvalid');
		expect(component.otherChangesList.length).toBe(0);
	});

	it('should add valid entry to the list', () => {
		// Fill valid form data
		component.otherChangesForm.patchValue({
			chargePaymentType: 'PREPAID' as any,
			entitlement: 'CARRIER',
			otherChargeCode: 'ABC123',
			otherChargeAmount: {
				currencyUnit: 'USD',
				numericalValue: 100,
			},
		});

		component.addOtherChanges();

		expect(component.otherChangesList.length).toBe(1);
		expect(component.otherChangesList[0]).toEqual({
			chargePaymentType: 'PREPAID',
			entitlement: 'CARRIER',
			otherChargeCode: 'ABC123',
			otherChargeAmount: {
				currencyUnit: 'USD',
				numericalValue: 100,
			},
		});
	});

	it('should delete items from the list', () => {
		// Add sample items
		component.otherChangesList = [
			{
				chargePaymentType: 'TYPE1',
				entitlement: 'ENT1',
				otherChargeCode: 'CODE1',
				otherChargeAmount: { currencyUnit: 'USD', numericalValue: 100 },
			},
			{
				chargePaymentType: 'TYPE2',
				entitlement: 'ENT2',
				otherChargeCode: 'CODE2',
				otherChargeAmount: { currencyUnit: 'EUR', numericalValue: 200 },
			},
		];

		component.deleteOtherCharge(0);

		expect(component.otherChangesList.length).toBe(1);
		expect(component.otherChangesList[0].otherChargeCode).toBe('CODE2');
	});

	it('should handle currency input correctly', () => {
		const currencyInput = fixture.debugElement.query(By.directive(CurrencyInputComponent));
		const currencyComponent = currencyInput.componentInstance as CurrencyInputComponent;

		// Test currency input initialization
		expect(currencyComponent.currencies).toEqual(mockCurrencies);

		// Simulate currency selection
		currencyComponent.currencyForm.get('currencyUnit')?.setValue('EUR');
		fixture.detectChanges();

		expect(component.otherChangesForm.get('otherChargeAmount.currencyUnit')?.value).toBe('EUR');
	});

	it('should require all fields before adding', () => {
		// Test partial form completion
		const testCases = [
			{ field: 'chargePaymentType', value: 'PREPAID' },
			{ field: 'entitlement', value: 'CARRIER' },
			{ field: 'otherChargeCode', value: 'ABC123' },
			{ field: 'otherChargeAmount', value: { currencyUnit: 'USD', numericalValue: 100 } },
		];

		for (const testCase of testCases) {
			component.otherChangesForm.reset();
			component.otherChangesForm.patchValue({ [testCase.field]: testCase.value });

			component.addOtherChanges();

			expect(notificationService.showError).toHaveBeenCalled();
			expect(component.otherChangesList.length).toBe(0);
		}
	});

	it('should validate numericalValue in otherChargeAmount', () => {
		// Set valid form except numericalValue
		component.otherChangesForm.patchValue({
			chargePaymentType: 'PREPAID' as any,
			entitlement: 'CARRIER',
			otherChargeCode: 'ABC123',
			otherChargeAmount: {
				currencyUnit: 'USD',
				numericalValue: null, // Invalid
			},
		});

		component.addOtherChanges();

		expect(component.otherChangesForm.get('otherChargeAmount')?.invalid).toBeTruthy();
		expect(notificationServiceShowError).toHaveBeenCalled();
	});

	it('should add multiple entries correctly', () => {
		const entries = [
			{
				chargePaymentType: 'TYPE1',
				entitlement: 'ENT1',
				otherChargeCode: 'CODE1',
				otherChargeAmount: { currencyUnit: 'USD', numericalValue: 100 },
			},
			{
				chargePaymentType: 'TYPE2',
				entitlement: 'ENT2',
				otherChargeCode: 'CODE2',
				otherChargeAmount: { currencyUnit: 'EUR', numericalValue: 200 },
			},
		];

		for (const entry of entries) {
			component.otherChangesForm.patchValue(entry as any);
			component.addOtherChanges();
		}

		expect(component.otherChangesList.length).toBe(2);
		expect(component.otherChangesList).toEqual(entries);
	});

	it('should validate currencyUnit in otherChargeAmount', () => {
		// Set valid form except currencyUnit
		component.otherChangesForm.patchValue({
			chargePaymentType: 'PREPAID' as any,
			entitlement: 'CARRIER',
			otherChargeCode: 'ABC123',
			otherChargeAmount: {
				currencyUnit: '',
				numericalValue: 100,
			},
		});

		component.addOtherChanges();

		expect(component.otherChangesForm.get('otherChargeAmount')?.invalid).toBeTrue();

		expect(notificationServiceShowError).toHaveBeenCalled();
	});
});
