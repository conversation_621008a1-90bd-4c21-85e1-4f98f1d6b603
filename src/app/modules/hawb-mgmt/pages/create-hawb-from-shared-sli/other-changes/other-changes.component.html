<div>
	<h3>{{ 'hawb.otherChanges.title'|translate }}</h3>

	<div class="form-container">
		<form [formGroup]="otherChangesForm">
			<div class="row">
				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-label>{{ 'hawb.otherChanges.chargePaymentType' | translate }}</mat-label>
					<mat-select formControlName="chargePaymentType">
						<mat-option value="Prepaid">Prepaid</mat-option>
						<mat-option value="Collect">Collect</mat-option>
					</mat-select>
					@if (otherChangesForm.get('chargePaymentType')?.hasError('required')) {
						<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.otherChanges.chargePaymentType' | translate } }}</mat-error>
					}
				</mat-form-field>
				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-label>{{ 'hawb.otherChanges.entitlement' | translate }}</mat-label>
					<orll-enum-code-form-item [enumType]="enumCodeTypeModel.ENTITLEMENT_CODE"
						formControlName="entitlement"></orll-enum-code-form-item>
					@if (otherChangesForm.get('entitlement')?.hasError('required')) {
						<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.otherChanges.entitlement' | translate } }}</mat-error>
					}
				</mat-form-field>
				<mat-form-field appearance="outline" class="col-3" floatLabel="always">
					<mat-label>{{ 'hawb.otherChanges.otherChargeCode' | translate }}</mat-label>
					<orll-enum-code-form-item [enumType]="enumCodeTypeModel.OTHER_CHARGE_CODE"
						formControlName="otherChargeCode"></orll-enum-code-form-item>
					@if (otherChangesForm.get('otherChargeCode')?.hasError('required')) {
						<mat-error>{{ 'validators.required'|translate:{ field: 'hawb.otherChanges.otherChargeCode' | translate } }}</mat-error>
					}
				</mat-form-field>
				<orll-currency-input
					class="col-3"
					formLabel="hawb.otherChanges.otherChargeAmount"
					[currencies]="currencies"
					[currencyForm]="otherChangesForm.controls.otherChargeAmount">
				</orll-currency-input>
			</div>
			<div class="d-flex flex-row">
				<button mat-raised-button type="button" (keydown.enter)="addOtherChanges()" (click)="addOtherChanges()"
					class="align-self-end add-button">
					<mat-icon>add</mat-icon>
					{{ 'hawb.otherChanges.addButton' | translate }}
				</button>
			</div>
		</form>
	</div>

	<div class="other-changes-list">
		@for (item of otherChangesList; track $index) {
			<div class="row">
				<div class="col cell-content col-charge_payment_type">
					<span class="label-name">{{ 'hawb.otherChanges.chargePaymentType'|translate }}: </span>
					<span>{{ item.chargePaymentType }} </span>
				</div>
				<mat-divider [vertical]="true"></mat-divider>
				<div class="col cell-content col-entitlement">
					<span class="label-name">{{ 'hawb.otherChanges.entitlement'|translate }}: </span>
					<span>{{ item.entitlement }} </span>
				</div>
				<mat-divider [vertical]="true"></mat-divider>
				<div class="col cell-content">
					<span class="label-name">{{ 'hawb.otherChanges.otherChargeCode'|translate }}: </span>
					<span>{{ item.otherChargeCode }} </span>
				</div>
				<mat-divider [vertical]="true"></mat-divider>
				<div class="col cell-content">
					<span class="label-name">{{ 'hawb.otherChanges.otherChargeAmount'|translate }}: </span>
					<span>{{ item.otherChargeAmount.numericalValue }} {{ item.otherChargeAmount.currencyUnit }} </span>
				</div>
				<mat-divider [vertical]="true"></mat-divider>
				<div class="col col-delete">
					<mat-icon (keydown.enter)="addOtherChanges()" (click)="deleteOtherCharge($index)"
						class="orll-piece-item-panel__delete-button">delete
					</mat-icon>
				</div>
			</div>
		}
	</div>
</div>
