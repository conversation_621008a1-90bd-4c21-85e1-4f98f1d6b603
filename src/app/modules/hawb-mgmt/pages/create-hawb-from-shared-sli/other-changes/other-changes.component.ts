import { Component, Input } from '@angular/core';
import { FormControl, FormGroup, NonNullableFormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatDividerModule } from '@angular/material/divider';
import { CurrencyInputComponent } from '@shared/components/currency-input/currency-input.component';
import { MatSelectModule } from '@angular/material/select';
import { NotificationService } from '@shared/services/notification.service';
import { EnumCodeFormItemComponent } from '@shared/components/enum-code-form-item/enum-code-form-item.component';
import { EnumCodeTypeModel } from '@shared/components/enum-code-form-item/enum-code-type.model';

export interface OtherChanges {
	chargePaymentType: string;
	entitlement: string;
	otherChargeCode: string;
	otherChargeAmount: {
		currencyUnit: string;
		numericalValue: number | null;
	};
}

@Component({
	selector: 'orll-other-changes',
	imports: [
		TranslateModule,
		ReactiveFormsModule,
		MatFormFieldModule,
		MatInputModule,
		MatButtonModule,
		MatIconModule,
		MatDividerModule,
		CurrencyInputComponent,
		MatSelectModule,
		EnumCodeFormItemComponent,
	],
	templateUrl: './other-changes.component.html',
	styleUrl: './other-changes.component.scss',
})
export class OtherChangesComponent {
	protected readonly enumCodeTypeModel = EnumCodeTypeModel;

	@Input()
	currencies: string[] = [];

	otherChangesForm = this.fb.group({
		chargePaymentType: [null, Validators.required],
		entitlement: ['', Validators.required],
		otherChargeCode: ['', Validators.required],
		otherChargeAmount: new FormGroup(
			{
				currencyUnit: new FormControl('', Validators.required),
				numericalValue: new FormControl<number | null>(null, Validators.required),
			},
			[Validators.required]
		),
	});

	otherChangesList: OtherChanges[] = [];

	constructor(
		private readonly fb: NonNullableFormBuilder,
		private readonly notificationService: NotificationService,
		private readonly translateService: TranslateService
	) {}

	addOtherChanges() {
		this.otherChangesForm.markAllAsTouched();
		if (this.otherChangesForm.invalid) {
			this.notificationService.showError(this.translateService.instant('hawb.otherChanges.formInvalid'));
			return;
		}
		this.otherChangesList.push(this.otherChangesForm.value as unknown as OtherChanges);
	}

	deleteOtherCharge(index: number) {
		this.otherChangesList.splice(index, 1);
	}
}
