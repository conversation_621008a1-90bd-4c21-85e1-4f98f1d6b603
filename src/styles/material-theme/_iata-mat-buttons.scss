@mixin iata-buttons-theme($theme) {
	button,
	a {
		--iata-mat-button-palette-color: var(--iata-blue-primary);

		&[color='accent'] {
			--iata-mat-button-palette-color: var(--iata-green-primary);
		}

		&[color='warn'] {
			--iata-mat-button-palette-color: var(--iata-red-primary);
		}

		&.mat-mdc-unelevated-button:not(:disabled) {
			--mdc-filled-button-container-color: var(--iata-mat-button-palette-color);
			--mdc-filled-button-label-text-color: var(--iata-white);
			border-radius: 1px;
		}

		&.mat-mdc-unelevated-button:disabled {
			--mdc-filled-button-disabled-container-color: var(--iata-grey-200);
			--mdc-filled-button-disabled-label-text-color: var(--iata-grey-400);
			border-radius: 1px;
		}

		&.mat-mdc-outlined-button:not(:disabled) {
			--mdc-outlined-button-label-text-color: var(--iata-mat-button-palette-color);
			--mdc-outlined-button-outline-color: var(--iata-mat-button-palette-color);
			border-radius: 1px;
			background-color: var(--iata-white);
		}

		&.mat-mdc-outlined-button:disabled {
			--mdc-outlined-button-label-text-color: var(--iata-grey-200);
			--mdc-outlined-button-outline-color: var(--iata-grey-400);
			border-radius: 1px;
		}

		&.mat-mdc-icon-button.mat-mdc-button-base {
			--mdc-icon-button-state-layer-size: 48px;
		}
	}
}
