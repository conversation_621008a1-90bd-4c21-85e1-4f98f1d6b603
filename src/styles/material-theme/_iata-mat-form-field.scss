@mixin iata-form-field-theme($theme) {
	.mat-mdc-form-field {
		--mat-form-field-container-height: 42px;
		--mat-form-field-container-vertical-padding: 7px;
		--mdc-outlined-text-field-outline-width: 1px;
		--mdc-outlined-text-field-focus-outline-width: 2px;
		--mdc-outlined-text-field-container-shape: 1px;
		--mdc-outlined-text-field-outline-color: var(--iata-grey-200);
		--mdc-outlined-text-field-label-text-color: var(--iata-grey-400);
		--mdc-outlined-text-field-input-text-color: var(--iata-grey-600);
		--mat-select-enabled-arrow-color: var(--iata-blue-primary);
		--mat-datepicker-toggle-icon-color: var(--iata-blue-primary);
		--mat-form-field-container-text-size: 14px;
		--mat-select-trigger-text-size: 14px;
		--mat-form-field-subscript-text-line-height: 14px;
		--mdc-outlined-text-field-label-text-size: 14px;
		--mat-form-field-outlined-label-text-populated-size: 12px;
		--mat-mdc-form-field-floating-label-scale: 1 !important;

		.mat-mdc-text-field-wrapper {
			background-color: var(--iata-grey-50);
		}

		.mat-mdc-form-field-icon-suffix {
			padding: 0;
		}

		.mat-mdc-icon-button.mat-mdc-button-base {
			--mdc-icon-button-state-layer-size: 42px;
			padding: var(--iata-gap-s);
		}

		&[readonly="true"] {
			pointer-events: none;
			cursor: not-allowed;

			.mat-mdc-floating-label,
			.mat-datepicker-toggle {
				--mat-datepicker-toggle-icon-color: var(--iata-grey-300);
				pointer-events: none !important;
			}

			.mdc-notched-outline {
				background-color: var(--iata-grey-50);
			}
		}

		&.iata-form-field--light {
			.mat-mdc-text-field-wrapper {
				background-color: var(--iata-white);
			}
		}
	}

	div.mat-mdc-select-panel {
		border-radius: 1px !important;
	}
}
