// eslint-disable-next-line
export const environment = {
	production: false,
	faqUrl: 'https://iata.org',
	supportUrl: 'https://iata.org',

	msalClientId: '09420a18-f9ea-45b4-916b-938c572a7f40',
	msalAuthority: 'https://iataazurenonprodb2c.b2clogin.com/iataazurenonprodb2c.onmicrosoft.com/B2C_1A_signup_signin',
	msalScopeBaseUrl: 'https://iataazurenonprodb2c.onmicrosoft.com/iata-unifra-api',
	msalAfterLoginUrl: 'http://localhost:4200',
	msalAfterLogoutUrl: 'https://iata--pprod.sandbox.my.site.com/csportal/s/login/?language=en_US',

	msalEnabled: false,
	backendEnabled: true,
	useBaseApi: true,
	baseApi: 'https://orll-test.iata-asd.com/prod-api',
	mfBaseUrl: 'http://localhost:4200/assets',

	userId: 'shipper1',
	orgId: '0b62001e-3b06-4d97-bc8b-182bd6efa613',

	userList: [
		{
			userId: 'shipper1',
			orgId: '0b62001e-3b06-4d97-bc8b-182bd6efa613',
		},
		{
			userId: 'forwarder1',
			orgId: '486c161d-23d2-468a-8234-26b31107a03f',
		},
	],
};
