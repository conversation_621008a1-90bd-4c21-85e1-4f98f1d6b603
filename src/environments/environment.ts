// eslint-disable-next-line
export const environment = {
	production: false,
	faqUrl: 'https://iata.org',
	supportUrl: 'https://iata.org',

	msalClientId: '09420a18-f9ea-45b4-916b-938c572a7f40',
	msalAuthority: 'https://iataazurenonprodb2c.b2clogin.com/iataazurenonprodb2c.onmicrosoft.com/B2C_1A_signup_signin',
	msalScopeBaseUrl: 'https://iataazurenonprodb2c.onmicrosoft.com/iata-unifra-api',
	msalAfterLoginUrl: 'http://localhost:4200',
	msalAfterLogoutUrl: 'https://iata--pprod.sandbox.my.site.com/csportal/s/login/?language=en_US',

	msalEnabled: false,
	backendEnabled: true,
	useBaseApi: false,
	baseApi: 'https://orll-test.iata-asd.com/prod-api',
	mfBaseUrl: 'http://localhost:4200/assets',

	userId: 'shipper1',
	orgId: 'ddbab271-1ddd-4cc4-b546-af9a0c0d4943',

	userList: [
		{
			userId: 'shipper1',
			orgId: 'ddbab271-1ddd-4cc4-b546-af9a0c0d4943',
		},
		{
			userId: 'forwarder1',
			orgId: '00dfa338-41a0-4d3f-9aab-ef309cf453de',
		},
	],
};
